namespace OSP.Notification.Application.Features.Template;

public class GetByIdHandler(IGenericRepository<Domain.Entities.Template, long> repository)
    : IRequestHandler<GetByIdQuery, TemplateDto>
{
    public async Task<TemplateDto> Handle(GetByIdQuery request, CancellationToken cancellationToken)
    {
        var template = await repository
            .Query(x => x.Id == request.Id)
            .Select(x => new TemplateDto
            {
                Id = x.Id,
                Code = x.Code,
                Description = x.Description,
            })
            .FirstOrDefaultAsync();

        if (template is null)
        {
            throw new EntityNotFoundException(nameof(Template), request.Id);
        }

        return template;
    }
}