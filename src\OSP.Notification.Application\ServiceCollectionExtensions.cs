﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace OSP.Notification.Application;

/// <summary>
///     Phương thức mở rộng để đăng ký các dịch vụ của Application vào container DI.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    ///     Đăng ký các dịch vụ của Application vào container DI.
    /// </summary>
    /// <param name="services">Collection dịch vụ.</param>
    /// <param name="configuration">C<PERSON>u hình để lấy chuỗi kết nối.</param>
    /// <returns>Collection dịch vụ đã được đăng ký.</returns>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services,
        IConfiguration configuration)
    {
        // Đăng ký MediatR handlers từ assembly hiện tại
        // services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ServiceCollectionExtensions).Assembly));

        // Đăng ký FluentValidation validators
        // services.AddValidatorsFromAssembly(typeof(ServiceCollectionExtensions).Assembly);

        return services;
    }
}