using OSP.Common.Domain.Enums;

namespace OSP.Notification.Application.Features.Template;

public class CreateContentCommand : IRequest<long>
{
    public long TemplateId { get; set; }
    public NotificationChannel Channel { get; set; }
    public string Language { get; set; } = "vi";
    public string? Title { get; set; }
    public string Content { get; set; }
}

public class CreateContentValidator : AbstractValidator<CreateContentCommand>
{
    public CreateContentValidator()
    {
        RuleFor(x => x.TemplateId).GreaterThan(0);
        RuleFor(x => x.Language).NotEmpty();
        RuleFor(x => x.Content).NotEmpty();
    }
}