namespace OSP.Notification.Application.Features.Template;

public class GetContentByTemplateIdHandler(IGenericRepository<TemplateContent, long> repository)
    : IRequestHandler<GetContentByTemplateIdQuery, IEnumerable<TemplateContentDto>>
{
    public async Task<IEnumerable<TemplateContentDto>> Handle(GetContentByTemplateIdQuery request,
        CancellationToken cancellationToken)
    {
        var contents = await repository.Query(x => x.TemplateId == request.TemplateId)
            .Select(x => new TemplateContentDto
            {
                Id = x.Id,
                TemplateId = x.TemplateId,
                Language = x.Language,
                Channel = x.Channel,
                Title = x.Title,
                Content = x.Content
            })
            .ToListAsync(cancellationToken);
        return contents;
    }
}