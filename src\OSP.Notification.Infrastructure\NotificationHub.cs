using System.Security.Claims;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

namespace OSP.Notification.Infrastructure;

public class NotificationHub(ILogger<NotificationHub> logger) : Hub
{
    public async Task NewMessage(long username, string message)
    {
        // lấy to<PERSON><PERSON> bộ claims
        var claims = Context.User?.Claims;

        // lấy UserId (nếu bạn dùng JWT, thường trong ClaimTypes.NameIdentifier hoặc "sub")
        var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        // lấy username
        var usernamex = Context.User?.Identity?.Name;

        logger.LogInformation("User connected: {UserId}, Username: {Username}", userId, usernamex);

        await Clients.All.SendAsync("messageReceived", userId, message);
    }
}