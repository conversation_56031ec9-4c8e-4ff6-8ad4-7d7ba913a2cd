using Microsoft.OpenApi.Models;
using OSP.Common.Domain.Extensions;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace OSP.Notification.Api.Swagger;

/// <summary>
/// Filter để tùy chỉnh operation trong Swagger
/// </summary>
public class SwaggerOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Thêm tag dựa trên controller name
        var controllerName = context.MethodInfo.DeclaringType?.Name.Replace("Controller", "");
        if (controllerName.IsNotEmpty())
        {
            operation.Tags = new List<OpenApiTag>
            {
                new() { Name = controllerName }
            };
        }

        // Thêm response mặc định cho các lỗi phổ biến
        if (!operation.Responses.ContainsKey("400"))
        {
            operation.Responses.Add("400", new OpenApiResponse
            {
                Description = "Bad Request - Dữ liệu đầu vào không hợp lệ"
            });
        }

        if (!operation.Responses.ContainsKey("401"))
        {
            operation.Responses.Add("401", new OpenApiResponse
            {
                Description = "Unauthorized - Chưa được xác thực"
            });
        }

        if (!operation.Responses.ContainsKey("403"))
        {
            operation.Responses.Add("403", new OpenApiResponse
            {
                Description = "Forbidden - Không có quyền truy cập"
            });
        }

        if (!operation.Responses.ContainsKey("404"))
        {
            operation.Responses.Add("404", new OpenApiResponse
            {
                Description = "Not Found - Không tìm thấy tài nguyên"
            });
        }

        if (!operation.Responses.ContainsKey("500"))
        {
            operation.Responses.Add("500", new OpenApiResponse
            {
                Description = "Internal Server Error - Lỗi hệ thống"
            });
        }

        // Thêm ví dụ cho request body nếu cần
        AddExamplesForRequestBody(operation, context);
    }

    private static void AddExamplesForRequestBody(OpenApiOperation operation, OperationFilterContext context)
    {
        if (operation.RequestBody?.Content is null)
        {
            return;
        }

        foreach (var content in operation.RequestBody.Content)
        {
            if (content.Key == "application/json" && content.Value.Schema?.Reference is not null)
            {
                // Có thể thêm logic để tạo example data ở đây
            }
        }
    }
}