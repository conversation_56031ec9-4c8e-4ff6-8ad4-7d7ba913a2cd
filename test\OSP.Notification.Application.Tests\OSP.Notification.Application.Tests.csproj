﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
      <RootNamespace>OSP.Notification.Application.Tests</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Exceptions\**" />
    <Compile Remove="Validators\**" />
    <EmbeddedResource Remove="Exceptions\**" />
    <EmbeddedResource Remove="Validators\**" />
    <None Remove="Exceptions\**" />
    <None Remove="Validators\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="FluentAssertions" Version="8.6.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="NUnit" Version="4.2.2" />
    <PackageReference Include="NUnit.Analyzers" Version="4.4.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.6.0" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="NUnit.Framework" />
  </ItemGroup>

  <ItemGroup>
      <ProjectReference Include="..\..\src\OSP.Notification.Application\OSP.Notification.Application.csproj"/>
  </ItemGroup>

</Project>
