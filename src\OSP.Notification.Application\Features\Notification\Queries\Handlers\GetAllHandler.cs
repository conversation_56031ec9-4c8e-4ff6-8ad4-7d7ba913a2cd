using OSP.Common.Domain.Enums;

namespace OSP.Notification.Application.Features.Notification;

public class GetAllHandler(IGenericRepository<UserNotification, Guid> repository)
    : IRequestHandler<GetAllQuery, IEnumerable<NotificationDto>>
{
    public async Task<IEnumerable<NotificationDto>> <PERSON>le(GetAllQuery request, CancellationToken cancellationToken)
    {
        return await repository.Query(x => x.UserId == request.UserId
                                           && x.Channel == NotificationChannel.Internal
                                           && x.Status == NotificationStatus.Sent)
            .OrderByDescending(x => x.CreatedAt)
            .Select(x => new NotificationDto
            {
                Id = x.Notification.Id,
                Title = x.Notification.Title!,
                Content = x.Notification.Content,
                SentAt = x.SentAt!.Value,
                IsRead = x.IsRead,
                ReadAt = x.ReadAt
            })
            .ToListAsync(cancellationToken);
    }
}