name: Reusable Flexible Runner

on:
  workflow_call:
    inputs:
      runner_choice:
        description: 'Runner được chỉ định từ workflow_dispatch'
        required: false
        type: string
        default: ''
      job_name:
        description: 'Tên job sẽ hiển thị'
        required: false
        type: string
        default: 'Flexible Runner Job'
      setup_dotnet:
        description: 'Có cần setup .NET không'
        required: false
        type: boolean
        default: false
      dotnet_version:
        description: 'Phiên bản .NET'
        required: false
        type: string
        default: '9.0'
      working_directory:
        description: 'Th<PERSON> mục làm việc'
        required: false
        type: string
        default: '.'
      run_command:
        description: 'Command cần chạy'
        required: false
        type: string
        default: 'echo "No command specified"'
      cache_dependencies:
        description: 'Có cache dependencies không'
        required: false
        type: boolean
        default: true
      install_dependencies:
        description: 'Có cài dependencies không (backend for .NET)'
        required: false
        type: string
        default: 'none' # none, backend
    secrets:
      LARK_CHAT_GROUP_NOTIFICATION:
        description: 'Lark webhook URL (nế<PERSON> cần)'
        required: false
    outputs:
      runner_used:
        description: 'Runner đã được sử dụng'
        value: ${{ jobs['flexible-runner'].outputs.runner_used }}
      job_result:
        description: '<PERSON>ế<PERSON> quả job'
        value: ${{ jobs['flexible-runner'].outputs.job_result }}

jobs:
  flexible-runner:
    name: ${{ inputs.job_name }}
    
    # Logic chọn runner linh hoạt:
    # 1. Ưu tiên input runner_choice từ workflow_dispatch
    # 2. Nếu không có, dùng repository variable DEFAULT_RUNNER_LABEL
    # 3. Fallback cuối cùng: ubuntu-latest
    runs-on: ${{ inputs.runner_choice || vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    
    outputs:
      runner_used: ${{ steps['runner-info'].outputs.runner_used }}
      job_result: ${{ steps['main-job'].outcome }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Display Runner Information
        id: runner-info
        run: |
          echo "🎉 Job is running on runner: ${{ runner.os }}-${{ runner.arch }}"
          echo "---"
          echo "🔍 Debug Information:"
          echo "Manual choice (from input): ${{ inputs.runner_choice }}"
          echo "Configured runner (from var): ${{ vars.DEFAULT_RUNNER_LABEL }}"
          echo "Final runner used: ${{ runner.os }}"
          echo "runner_used=${{ runner.os }}" >> $GITHUB_OUTPUT

      # Conditional .NET Setup
      - name: Set up .NET ${{ inputs.dotnet_version }}
        if: inputs.setup_dotnet == true
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ inputs.dotnet_version }}

      # Cache NuGet packages (if .NET is enabled)
      - name: Cache NuGet packages
        if: inputs.setup_dotnet == true && inputs.cache_dependencies == true
        uses: actions/cache@v4
        with:
          path: |
            ~/.nuget/packages
            ~/.local/share/NuGet/Cache
            ~/AppData/Local/NuGet/Cache
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/*.fsproj', '**/*.vbproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      # Verify Environment Versions
      - name: Verify environment versions
        if: inputs.setup_dotnet == true
        run: |
          echo "=== Environment Versions ==="
          SETUP_DOTNET="${{ inputs.setup_dotnet }}"
          
          if [[ "$SETUP_DOTNET" == "true" ]]; then
            echo ".NET version:"
            dotnet --version
            echo ".NET info:"
            dotnet --info
          fi
          echo "============================="

      # Install Backend Dependencies (.NET)
      - name: Install Backend Dependencies (.NET)
        if: inputs.install_dependencies == 'backend'
        working-directory: ${{ inputs.working_directory }}
        run: |
          echo "📦 Installing .NET Backend dependencies..."
          dotnet restore
          echo "✅ .NET Backend dependencies installed successfully!"

      # Main Job Execution
      - name: Execute Main Command
        id: main-job
        working-directory: ${{ inputs.working_directory }}
        run: |
          echo "🚀 Executing main command..."
          echo "Command: ${{ inputs.run_command }}"
          echo "Working directory: ${{ inputs.working_directory }}"
          echo "---" 
          ${{ inputs.run_command }}