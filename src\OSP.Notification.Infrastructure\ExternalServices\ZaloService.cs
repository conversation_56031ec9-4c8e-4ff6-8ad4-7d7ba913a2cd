using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;
using OSP.Notification.Domain.Config;

namespace OSP.Notification.Infrastructure.ExternalServices;

public class ZaloService(
    IHttpClientFactory httpClientFactory,
    IOptions<ZaloOptions> options,
    ILogger<ZaloService> logger)
    : IZaloService
{
    public async Task SendAsync(string phoneNumber, string templateId, Dictionary<string, string> templateData)
    {
        var httpClient = httpClientFactory.CreateClient();
        httpClient.DefaultRequestHeaders.Add("access_token", options.Value.AccessToken);

        var requestBody = new
        {
            phone = phoneNumber,
            template_id = templateId,
            template_data = templateData,
            tracking_id = options.Value.TrackingId
        };

        var response =
            await httpClient.PostAsJsonAsync("https://business.openapi.zalo.me/message/template", requestBody);

        if (response.IsSuccessStatusCode)
        {
            var json = await response.Content.ReadAsStringAsync();
            var error = JsonDocument.Parse(json).RootElement.GetProperty("error").GetInt32();
            var message = JsonDocument.Parse(json).RootElement.GetProperty("message").GetString();

            if (error != 0)
            {
                logger.LogError("Failed to send ZNS via Zalo. Error={Error}, Message={Message}", error, message);
                throw new InvalidOperationException($"Failed to send ZNS via Zalo. Error={error}, Message={message}");
            }

            logger.LogInformation("ZNS sent to {PhoneNumber}. TemplateId={TemplateId}, TemplateData={TemplateData}",
                phoneNumber, templateId, string.Join(",", templateData.Select(kv => $"{kv.Key}={kv.Value}")));
        }
        else
        {
            var body = await response.Content.ReadAsStringAsync();
            logger.LogError("Failed to send ZNS via Zalo. StatusCode={Status}, Body={Body}",
                response.StatusCode, body);
            throw new InvalidOperationException(
                "Failed to send ZNS via Zalo. StatusCode={response.StatusCode}, Body={body}");
        }
    }

    public async Task SendBatchAsync(ZnsMessage message, int maxParallel = 5,
        CancellationToken cancellationToken = default)
    {
        using var semaphore = new SemaphoreSlim(maxParallel);

        var tasks = message.phoneNumbers.Select(async phone =>
        {
            await semaphore.WaitAsync();
            try
            {
                await SendAsync(phone, message.templateId, message.templateData);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
    }
}