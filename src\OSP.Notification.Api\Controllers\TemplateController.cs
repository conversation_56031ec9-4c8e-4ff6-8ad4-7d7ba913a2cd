using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OSP.Common.Api.Controllers;
using OSP.Common.Domain.Enums;
using OSP.Notification.Application.Features.Template;
using OSP.Notification.Application.Models;

namespace OSP.Notification.Api.Controllers;

[ApiVersion(1)]
[AllowAnonymous]
public class TemplateController(IMediator mediator) : BaseRestApi(mediator)
{
    private readonly IMediator _mediator = mediator;

    /// <summary>
    /// L<PERSON>y danh sách tất cả templates
    /// </summary>
    /// <returns>Danh sách templates</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TemplateDto>>> GetAll()
    {
        var result = await _mediator.Send(new GetAllQuery());
        return Ok(result);
    }

    /// <summary>
    /// Lấy thông tin template theo ID
    /// </summary>
    /// <param name="id">ID của template</param>
    /// <returns>Thông tin template</returns>
    [HttpGet("{id:long}")]
    public async Task<ActionResult<TemplateDto>> GetById(long id)
    {
        var result = await _mediator.Send(new GetByIdQuery(id));
        return Ok(result);
    }

    /// <summary>
    /// Tạo template mới
    /// </summary>
    /// <param name="command">Thông tin tạo template</param>
    /// <returns>ID của template đã tạo</returns>
    [HttpPost]
    public async Task<ActionResult<long>> Create([FromBody] CreateTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Cập nhật thông tin template
    /// </summary>
    /// <param name="id">ID của template</param>
    /// <param name="command">Thông tin cập nhật template</param>
    /// <returns>Kết quả thành công</returns>
    [HttpPut("{id:long}")]
    public async Task<ActionResult<bool>> Update(long id, [FromBody] UpdateTemplateCommand command)
    {
        var updateCommand = command with { Id = id };
        var result = await _mediator.Send(updateCommand);
        return Ok(result);
    }

    /// <summary>
    /// Xóa template
    /// </summary>
    /// <param name="id">ID của template</param>
    /// <returns>Kết quả thành công</returns>
    [HttpDelete("{id:long}")]
    public async Task<ActionResult<bool>> Delete(long id)
    {
        var result = await _mediator.Send(new DeleteTemplateCommand(id));
        return Ok(result);
    }

    // Content Management Endpoints

    /// <summary>
    /// Lấy danh sách content theo template ID
    /// </summary>
    /// <param name="templateId">ID của template</param>
    /// <returns>Danh sách content của template</returns>
    [HttpGet("{templateId:long}/Content")]
    public async Task<ActionResult<IEnumerable<TemplateContentDto>>> GetContentByTemplateId(long templateId)
    {
        var result = await _mediator.Send(new GetContentByTemplateIdQuery(templateId));
        return Ok(result);
    }

    /// <summary>
    /// Lấy content theo code, channel và language
    /// </summary>
    /// <param name="code">Code của template</param>
    /// <param name="channel">Channel notification</param>
    /// <param name="language">Ngôn ngữ (mặc định: vi)</param>
    /// <returns>Thông tin content</returns>
    [HttpGet("Content/ByCode/{code}")]
    public async Task<ActionResult<TemplateContentDto>> GetContentByCode(
        string code,
        [FromQuery] NotificationChannel channel,
        [FromQuery] string language = "vi")
    {
        var result = await _mediator.Send(new GetContentByCodeQuery(code, channel, language));
        return Ok(result);
    }

    /// <summary>
    /// Lấy content theo ID
    /// </summary>
    /// <param name="contentId">ID của content</param>
    /// <returns>Thông tin content</returns>
    [HttpGet("Content/{contentId:long}")]
    public async Task<ActionResult<TemplateContentDto>> GetContentById(long contentId)
    {
        var result = await _mediator.Send(new GetContentByIdQuery(contentId));
        return Ok(result);
    }

    /// <summary>
    /// Tạo content mới cho template
    /// </summary>
    /// <param name="command">Thông tin tạo content</param>
    /// <returns>ID của content đã tạo</returns>
    [HttpPost("Content")]
    public async Task<ActionResult<long>> CreateContent([FromBody] CreateContentCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Cập nhật thông tin content
    /// </summary>
    /// <param name="contentId">ID của content</param>
    /// <param name="command">Thông tin cập nhật content</param>
    /// <returns>Kết quả thành công</returns>
    [HttpPut("Content/{contentId:long}")]
    public async Task<ActionResult<bool>> UpdateContent(long contentId, [FromBody] UpdateContentCommand command)
    {
        command.Id = contentId;
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Xóa content
    /// </summary>
    /// <param name="contentId">ID của content</param>
    /// <returns>Kết quả thành công</returns>
    [HttpDelete("Content/{contentId:long}")]
    public async Task<ActionResult<bool>> DeleteContent(long contentId)
    {
        var result = await _mediator.Send(new DeleteContentCommand(contentId));
        return Ok(result);
    }
}