# OSP Notification Service

Dịch vụ thông báo (Notification Service) được xây dựng theo kiến trúc Clean Architecture và Domain-Driven Design (DDD), hỗ trợ gửi thông báo qua nhiều kênh khác nhau bao gồm <PERSON>, SMS, và Real-time notifications thông qua SignalR.

## Tính năng chính

- **Quản lý thông báo**: Tạo, đọc, cập nhật trạng thái thông báo
- **Multi-channel notifications**: Hỗ trợ <PERSON>ail (SMTP/SendGrid), SMS (Stringee), Real-time (SignalR)
- **Template system**: Quản lý template cho các loại thông báo khác nhau
- **Kafka integration**: Xử lý thông báo bất đồng bộ qua Kafka
- **Real-time notifications**: SignalR Hub cho thông báo thời gian thực
- **Authentication & Authorization**: <PERSON><PERSON><PERSON> hợ<PERSON> với <PERSON>cloak SSO

## Kiến trúc

Dự án được tổ chức theo Clean Architecture với 4 layers:

- **OSP.Notification.Api**: Web API layer, controllers, SignalR hubs
- **OSP.Notification.Application**: Business logic, CQRS handlers, services
- **OSP.Notification.Domain**: Domain entities, value objects
- **OSP.Notification.Infrastructure**: Data access, external services, repositories

## Yêu cầu hệ thống

- **.NET 9.0** hoặc cao hơn
- **PostgreSQL** (database)
- **Redis** (caching)
- **Kafka** (message broker)
- **osp-common-be-dotnet** (shared libraries)

## Cài đặt và cấu hình

### 1. Chuẩn bị môi trường

```bash
# Clone repo osp-common-be-dotnet về cùng thư mục với notification service
git clone https://github.com/your-org/osp-common-be-dotnet.git
git clone https://github.com/your-org/osp-notification-service.git

# Cấu trúc thư mục:
# ├── osp-common-be-dotnet/
# └── osp-notification-service/
```

### 2. Cấu hình Database

Tạo database PostgreSQL và cập nhật connection string trong `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=notification_db;Username=postgres;Password=your_password"
  }
}
```

### 3. Cấu hình Email Provider

#### SMTP Configuration:
```json
{
  "Email": {
    "Provider": "Smtp",
    "Smtp": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "UseSsl": true,
      "Accounts": [
        {
          "Username": "<EMAIL>",
          "Password": "your-app-password"
        }
      ],
      "DefaultFromAddress": "<EMAIL>",
      "DefaultFromName": "Your Company"
    }
  }
}
```

#### SendGrid Configuration:
```json
{
  "Email": {
    "Provider": "SendGrid",
    "SendGrid": {
      "ApiKey": "your-sendgrid-api-key",
      "DefaultFromAddress": "<EMAIL>",
      "DefaultFromName": "Your Company"
    }
  }
}
```

### 4. Cấu hình SMS Provider (Stringee)

```json
{
  "Sms": {
    "Provider": "Stringee",
    "Stringee": {
      "ApiKeySid": "your-stringee-api-key-sid",
      "ApiKeySecret": "your-stringee-api-key-secret",
      "From": "Your Brand"
    }
  }
}
```

### 5. Cấu hình Kafka

```json
{
  "Kafka": {
    "BootstrapServers": "localhost:9092"
  }
}
```

### 6. Cấu hình Swagger/OpenAPI

```json
{
  "Swagger": {
    "Authority": "https://your-keycloak-server/realms/master",
    "AuthorizationUrl": "https://your-keycloak-server/realms/master/protocol/openid-connect/auth",
    "TokenUrl": "https://your-keycloak-server/realms/master/protocol/openid-connect/token",
    "ClientId": "notification-swagger-service",
    "Scope": "openid profile"
  }
}
```

## Chạy ứng dụng

### 1. Development Environment

```bash
# Restore dependencies
dotnet restore

# Build solution
dotnet build

# Run migrations (nếu cần)
dotnet ef database update --project src/OSP.Notification.Api

# Start API
dotnet run --project src/OSP.Notification.Api
```

### 2. Production Environment

```bash
# Build for production
dotnet publish src/OSP.Notification.Api -c Release -o ./publish

# Run published app
dotnet ./publish/OSP.Notification.Api.dll
```

## API Endpoints

### Notification Management

- `GET /api/v1/notification` - Lấy tất cả thông báo của user
- `GET /api/v1/notification/{id}` - Lấy thông báo theo ID
- `GET /api/v1/notification/unread` - Lấy thông báo chưa đọc
- `GET /api/v1/notification/unread/count` - Đếm số thông báo chưa đọc
- `POST /api/v1/notification` - Tạo thông báo mới
- `PATCH /api/v1/notification/{id}/markread` - Đánh dấu đã đọc
- `PATCH /api/v1/notification/markallread` - Đánh dấu tất cả đã đọc

### Template Management

- `GET /api/v1/template` - Lấy danh sách template
- `GET /api/v1/template/{id}` - Lấy template theo ID
- `POST /api/v1/template` - Tạo template mới
- `PUT /api/v1/template/{id}` - Cập nhật template
- `DELETE /api/v1/template/{id}` - Xóa template

### SignalR Hub

- **Endpoint**: `/NotificationHub`
- **Authentication**: Required (JWT token)
- **Methods**:
  - `NewMessage(username, message)` - Gửi tin nhắn mới
  - `messageReceived` - Nhận tin nhắn (client event)

## Sử dụng dịch vụ

### 1. Tạo thông báo qua API

```bash
curl -X POST "https://localhost:5001/api/v1/notification" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Thông báo mới",
    "content": "Nội dung thông báo",
    "userIds": ["user-id-1", "user-id-2"],
    "channels": ["Email", "Sms", "InApp"]
  }'
```

### 2. Gửi thông báo qua Kafka

```json
{
  "title": "Thông báo từ Kafka",
  "content": "Nội dung thông báo",
  "userIds": ["user-id-1"],
  "channels": ["InApp"],
  "templateId": "template-id-optional"
}
```

### 3. Kết nối SignalR (JavaScript)

```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/NotificationHub", {
        accessTokenFactory: () => "your-jwt-token"
    })
    .build();

// Lắng nghe thông báo
connection.on("messageReceived", (userId, message) => {
    console.log(`Received from ${userId}: ${message}`);
});

// Kết nối
await connection.start();

// Gửi tin nhắn
await connection.invoke("NewMessage", "username", "Hello World!");
```

## Testing

```bash
# Chạy unit tests
dotnet test

# Chạy tests với coverage
dotnet test --collect:"XPlat Code Coverage"
```

## Monitoring và Logging

Dịch vụ sử dụng Microsoft.Extensions.Logging với các log levels:
- **Information**: Hoạt động bình thường
- **Warning**: Cảnh báo không nghiêm trọng
- **Error**: Lỗi cần xử lý
- **Debug**: Thông tin debug (chỉ trong Development)

## Troubleshooting

### Lỗi thường gặp

1. **Connection string không đúng**: Kiểm tra PostgreSQL connection
2. **Kafka không kết nối được**: Đảm bảo Kafka đang chạy trên port 9092
3. **SignalR authentication failed**: Kiểm tra JWT token và CORS settings
4. **Email gửi không thành công**: Kiểm tra SMTP settings hoặc SendGrid API key

### Debug

```bash
# Xem logs chi tiết
export ASPNETCORE_ENVIRONMENT=Development
dotnet run --project src/OSP.Notification.Api --verbosity detailed
```

## Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## License

Distributed under the MIT License. See `LICENSE` for more information.
