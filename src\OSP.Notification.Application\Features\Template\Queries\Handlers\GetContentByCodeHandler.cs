namespace OSP.Notification.Application.Features.Template;

public class GetContentByCodeHandler(IGenericRepository<TemplateContent, long> repository)
    : IRequestHandler<GetContentByCodeQuery, TemplateContentDto>
{
    public async Task<TemplateContentDto> Handle(GetContentByCodeQuery request, CancellationToken cancellationToken)
    {
        var content = await repository.Query(x
                => x.Template.Code == request.Code
                   && x.Language == request.Language
                   && x.Channel == request.Channel
            )
            .Select(x => new TemplateContentDto
            {
                Id = x.Id,
                TemplateId = x.TemplateId,
                Language = x.Language,
                Channel = x.Channel,
                Title = x.Title,
                Content = x.Content
            })
            .FirstOrDefaultAsync();

        if (content is null)
        {
            throw new EntityNotFoundException(nameof(TemplateContent), new Dictionary<string, string>
            {
                { "Code", request.Code },
                { "Language", request.Language },
                { "Channel", request.Channel.ToString() }
            });
        }

        return (content);
    }
}