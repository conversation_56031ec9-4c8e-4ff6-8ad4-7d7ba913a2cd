namespace OSP.Notification.Application.Models;

public class EmailMessage
{
    public IEnumerable<string> To { get; set; }
    public IEnumerable<string> Cc { get; set; } = [];
    public IEnumerable<string> Bcc { get; set; } = [];
    public string Subject { get; set; }
    public string Body { get; set; }
    public bool IsHtml { get; set; } = true;
    public IEnumerable<EmailAttachment> Attachments { get; set; } = [];
}