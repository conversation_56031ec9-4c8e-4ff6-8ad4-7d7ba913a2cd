namespace OSP.Notification.Application.Features.Template;

public record UpdateTemplateCommand(long Id, string Code, string Description) : IRequest<bool>;

public class UpdateTemplateValidator : AbstractValidator<UpdateTemplateCommand>
{
    public UpdateTemplateValidator()
    {
        RuleFor(x => x.Id).GreaterThan(0);
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
    }
}