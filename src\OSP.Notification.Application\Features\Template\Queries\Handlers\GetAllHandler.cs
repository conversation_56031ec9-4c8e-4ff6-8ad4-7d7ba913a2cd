namespace OSP.Notification.Application.Features.Template;

public class GetAll<PERSON><PERSON><PERSON>(IGenericRepository<Domain.Entities.Template, long> repository)
    : IRequestHandler<GetAllQuery, IEnumerable<TemplateDto>>
{
    public async Task<IEnumerable<TemplateDto>> Handle(GetAllQuery request, CancellationToken cancellationToken)
    {
        var templates = await repository
            .Query()
            .Select(x => new TemplateDto
            {
                Id = x.Id,
                Code = x.Code,
                Description = x.Description
            })
            .ToListAsync();

        return templates;
    }
}