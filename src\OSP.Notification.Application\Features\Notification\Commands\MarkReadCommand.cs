namespace OSP.Notification.Application.Features.Notification;

public class MarkReadCommand : IRequest
{
    public Guid NotificationId { get; set; }
    public Guid UserId { get; set; }
    public bool IsRead { get; set; } = true;
}

public class MarkReadValidator : AbstractValidator<MarkReadCommand>
{
    public MarkReadValidator()
    {
        RuleFor(x => x.NotificationId).NotEmpty();
        RuleFor(x => x.UserId).NotEmpty();
    }
}