using OSP.Common.Domain.Entities;
using OSP.Common.Domain.Enums;

namespace OSP.Notification.Domain.Entities;

public class TemplateContent : BaseDomainEntity<long>, IHasCreatedBy, IHasModifiedBy
{
    public NotificationChannel Channel { get; set; }
    public string Language { get; set; } = "vi";
    public string? Title { get; set; }
    public string Content { get; set; }

    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTime ModifiedAt { get; set; }
    public Guid ModifiedBy { get; set; }

    public long TemplateId { get; set; }
    public virtual Template Template { get; set; }
}