﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>OSP.Notification.Application</RootNamespace>
    </PropertyGroup>

    <PropertyGroup>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\osp-common-be-dotnet\src\OSP.Common.Application\OSP.Common.Application.csproj"/>
        <ProjectReference Include="..\..\..\osp-common-be-dotnet\src\OSP.Common.Domain\OSP.Common.Domain.csproj"/>
        <ProjectReference Include="..\OSP.Notification.Domain\OSP.Notification.Domain.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Features\Notification\"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="SendGrid" Version="9.29.3"/>
        <PackageReference Include="SmartFormat.NET" Version="3.6.1"/>
    </ItemGroup>

</Project>
