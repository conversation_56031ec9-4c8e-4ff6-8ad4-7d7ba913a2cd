namespace OSP.Notification.Application.Features.Template;

public class Delete<PERSON>ontent<PERSON><PERSON><PERSON>(IGenericRepository<TemplateContent, long> repository)
    : IRequestHandler<DeleteContentCommand, bool>
{
    public async Task<bool> Handle(DeleteContentCommand request, CancellationToken cancellationToken)
    {
        return await repository
            .Query(x => x.Id == request.Id)
            .ExecuteDeleteAsync() > 0;
    }
}