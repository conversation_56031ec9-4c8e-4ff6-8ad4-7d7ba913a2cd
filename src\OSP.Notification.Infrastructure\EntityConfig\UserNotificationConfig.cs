using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace OSP.Notification.Infrastructure.EntityConfig;

public class UserNotificationConfig : IEntityTypeConfiguration<UserNotification>
{
    public void Configure(EntityTypeBuilder<UserNotification> builder)
    {
        builder.Property(x => x.ErrorMessage).HasColumnType("text");
        builder.Property(x => x.IsRead).HasDefaultValue(false);

        builder.HasIndex(x => new { x.UserId, x.IsRead });

        builder.HasOne(x => x.Notification)
            .WithMany(n => n.UserNotification)
            .HasForeignKey(x => x.NotificationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}