using System.Reflection;
using Microsoft.OpenApi.Models;
using OSP.Notification.Api.Swagger;
using OSP.Notification.Domain.Config;

namespace OSP.Notification.Api;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddApiServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterSwagger(configuration);

        return services;
    }

    public static IServiceCollection RegisterSwagger(this IServiceCollection services, IConfiguration configuration)
    {
        var swaggerConfig = new SwaggerOptions();
        configuration.GetSection("Swagger").Bind(swaggerConfig);

        services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v1", new OpenApiInfo
            {
                Version = "v1.0.0",
                Title = "OSP Notification Service API",
                Description = "### OSP Notification Service - Multi-Channel Notification Management System\n\n" +
                              "Dịch vụ quản lý thông báo đa kênh cho hệ thống OSP, hỗ trợ gửi và quản lý thông báo qua các kênh:\n\n" +
                              "**<PERSON><PERSON>c kênh hỗ trợ:**\n" +
                              "- **Email** - Gửi email thông báo với template tùy chỉnh\n" +
                              "- **SMS** - Gửi tin nhắn SMS qua nhà cung cấp\n" +
                              "- **Push Notification** - Thông báo đẩy trên web app\n" +
                              "- **Zalo** - Gửi tin nhắn qua Zalo OA\n\n" +
                              "**Tính năng chính:**\n" +
                              "- 📧 **Notification Management** - Quản lý thông báo người dùng\n" +
                              "- 📝 **Template Management** - Quản lý template và nội dung đa ngôn ngữ\n" +
                              "- 🔔 **Real-time Updates** - Cập nhật thời gian thực qua SignalR\n" +
                              "- 🌐 **Multi-language Support** - Hỗ trợ đa ngôn ngữ\n" +
                              "- 📊 **Tracking & Analytics** - Theo dõi trạng thái gửi và đọc\n\n" +
                              "**Kiến trúc:** Được xây dựng theo **Clean Architecture** và **Domain-Driven Design (DDD)** với .NET 9.0",
                Contact = new OpenApiContact
                {
                    Name = "SWAT Team",
                    Email = "<EMAIL>",
                    Url = new Uri("https://osp.vn")
                },
                License = new OpenApiLicense
                {
                    Name = "MIT License",
                    Url = new Uri("https://opensource.org/licenses/MIT")
                }
            });

            // Cấu hình security scheme cho JWT Bearer
            options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description =
                    "JWT Authorization header sử dụng Bearer scheme. Ví dụ: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer",
                BearerFormat = "JWT"
            });

            // Cấu hình OAuth2 (Bearer Token)
            options.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
            {
                Type = SecuritySchemeType.OAuth2,
                Flows = new OpenApiOAuthFlows
                {
                    AuthorizationCode = new OpenApiOAuthFlow
                    {
                        AuthorizationUrl =
                            new Uri(swaggerConfig.AuthorizationUrl), // URL authorize của IdentityServer / Keycloak
                        TokenUrl = new Uri(swaggerConfig.TokenUrl), // URL lấy token
                        Scopes = new Dictionary<string, string>()
                    }
                }
            });

            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    []
                }
            });

            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "oauth2"
                        }
                    },
                    []
                }
            });

            // Include XML comments từ assembly hiện tại
            var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFilename);
            if (File.Exists(xmlPath))
            {
                options.IncludeXmlComments(xmlPath);
            }

            // Tùy chỉnh schema cho enum
            options.SchemaFilter<EnumSchemaFilter>();

            // Tùy chỉnh operation để hiển thị thông tin rõ ràng hơn
            options.OperationFilter<SwaggerOperationFilter>();
        });

        return services;
    }
}