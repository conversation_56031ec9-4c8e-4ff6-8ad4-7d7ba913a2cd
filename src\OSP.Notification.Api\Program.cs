﻿using System.Reflection;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using OSP.Common.Api;
using OSP.Common.Application;
using OSP.Common.Infrastructure;
using OSP.Common.Infrastructure.Extensions;
using OSP.Notification.Api;
using OSP.Notification.Api.Consumers;
using OSP.Notification.Application;
using OSP.Notification.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Configuration.ConfigureConfiguration(
    builder.Configuration.GetServiceCode(),
    builder.Configuration.GetServicePort(),
    false,
    builder.Configuration.GetPascalServiceCode(builder.Configuration.GetServiceCode())
);

// Đăng ký Common API services
builder.Services.AddCommonApiServices(builder.Configuration, options =>
    {
        options.Events = new JwtBearerEvents
        {
            OnMessageReceived = context =>
            {
                var accessToken = context.Request.Query["access_token"];

                // If the request is for our hub...
                var path = context.HttpContext.Request.Path;
                if (!string.IsNullOrWhiteSpace(accessToken) &&
                    (path.StartsWithSegments("/NotificationHub")))
                {
                    // Read the token out of the query string
                    context.Token = accessToken;
                }

                return Task.CompletedTask;
            }
        };
    })
    .AddCommonApplicationServices(builder.Configuration)
    .AddCommonInfrastructureServices(builder.Configuration)
    .AddCommonGraphQL(Assembly.GetExecutingAssembly());

// Đăng ký cho current service
builder.Services.AddApiServices(builder.Configuration)
    .AddApplicationServices(builder.Configuration)
    .AddInfrastructureServices(builder.Configuration);

builder.Services.AddSignalR();

// Thêm BackgroundService
builder.Services.AddHostedService<KafkaConsumerService>();

var app = builder.Build();

app.UseCommonApiServices(builder.Configuration);
app.MapHub<NotificationHub>("/NotificationHub")
    .RequireAuthorization() // bắt buộc có token
    .RequireCors(); // áp cors chỉ cho hub này

await app.RunAsync();