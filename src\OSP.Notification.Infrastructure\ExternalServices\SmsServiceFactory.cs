using Microsoft.Extensions.Options;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;
using OSP.Notification.Domain.Config;

namespace OSP.Notification.Infrastructure.ExternalServices;

public class SmsServiceFactory(
    StringeeSmsService stringeeSmsService,
    IOptions<SmsProviderOptions> options) : ISmsService
{
    private readonly SmsProviderOptions _options = options.Value;

    public Task SendAsync(SmsMessage message, CancellationToken cancellationToken = default)
    {
        return _options.Provider switch
        {
            "Stringee" => stringeeSmsService.SendAsync(message, cancellationToken),
            _ => throw new InvalidOperationException($"Unknown sms provider: {_options.Provider}")
        };
    }

    public Task SendBatchAsync(SmsMessage message, int batchSize = 100, int maxParallel = 5,
        CancellationToken cancellationToken = default)
    {
        return _options.Provider switch
        {
            "Stringee" => stringeeSmsService.SendBatchAsync(message, batchSize, maxParallel, cancellationToken),
            _ => throw new InvalidOperationException($"Unknown sms provider: {_options.Provider}")
        };
    }
}