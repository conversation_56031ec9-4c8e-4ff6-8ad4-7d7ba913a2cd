using OSP.Common.Application.Contexts;

namespace OSP.Notification.Application.Features.Template;

public class UpdateContent<PERSON>andler(
    IGenericRepository<TemplateContent, long> repository,
    IRequestContext requestContext)
    : IRequestHandler<UpdateContentCommand, bool>
{
    public async Task<bool> Handle(UpdateContentCommand request, CancellationToken cancellationToken)
    {
        var now = DateTime.Now;
        return await repository
            .Query(x => x.Id == request.Id)
            .ExecuteUpdateAsync(x
                => x.SetProperty(x => x.Channel, request.Channel)
                    .SetProperty(x => x.Language, request.Language)
                    .SetProperty(x => x.Title, request.Title)
                    .SetProperty(x => x.Content, request.Content)
                    .SetProperty(p => p.ModifiedAt, now)
                    .SetProperty(p => p.ModifiedBy, requestContext.UserId)
            ) > 0;
    }
}