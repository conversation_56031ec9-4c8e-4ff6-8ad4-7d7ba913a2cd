using OSP.Common.Application.Contexts;
using OSP.Common.Domain.Enums;

namespace OSP.Notification.Application.Features.Notification;

public class Mark<PERSON><PERSON><PERSON><PERSON>Handler(
    IGenericRepository<UserNotification, Guid> repository,
    IRequestContext requestContext)
    : IRequestHandler<MarkAllReadCommand>
{
    public Task Handle(MarkAllReadCommand request, CancellationToken cancellationToken)
    {
        var now = DateTime.Now;

        return repository.Query()
            .Where(x => x.UserId == request.UserId
                        && x.Channel == NotificationChannel.Internal
                        && x.Status == NotificationStatus.Sent)
            .ExecuteUpdateAsync(x
                => x.SetProperty(p => p.IsRead, p => request.IsRead)
                    .SetProperty(p => p.ReadAt, p => now)
                    .SetProperty(p => p.ModifiedAt, now)
                    .SetProperty(p => p.ModifiedBy, requestContext.UserId), cancellationToken);
    }
}