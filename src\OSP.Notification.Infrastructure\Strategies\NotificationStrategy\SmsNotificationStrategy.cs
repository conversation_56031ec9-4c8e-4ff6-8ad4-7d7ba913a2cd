using OSP.Common.Domain.Enums;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;
using OSP.Notification.Application.Strategies.NotificationStrategy;

namespace OSP.Notification.Infrastructure.Strategies.NotificationStrategy;

public class SmsNotificationStrategy(ISmsService smsService) : INotificationStrategy
{
    public NotificationChannel Channel => NotificationChannel.SMS;

    public Task SendAsync(NotificationPayload payload)
    {
        return smsService.SendBatchAsync(new SmsMessage
        {
            To = payload.PhoneNumbers,
            Content = payload.Content
        });
    }
}