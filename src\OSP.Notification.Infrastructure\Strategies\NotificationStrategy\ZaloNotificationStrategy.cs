using OSP.Common.Domain.Enums;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;
using OSP.Notification.Application.Strategies.NotificationStrategy;

namespace OSP.Notification.Infrastructure.Strategies.NotificationStrategy;

public class ZaloNotificationStrategy(IZaloService zaloService) : INotificationStrategy
{
    public NotificationChannel Channel => NotificationChannel.Zalo;

    public Task SendAsync(NotificationPayload payload)
    {
        return zaloService.SendBatchAsync(new ZnsMessage
        {
            phoneNumbers = payload.PhoneNumbers,
            templateId = payload.Content,
            templateData = payload.Data
        });
    }
}