using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace OSP.Notification.Infrastructure.EntityConfig;

public class NotificationConfig : IEntityTypeConfiguration<Domain.Entities.Notification>
{
    public void Configure(EntityTypeBuilder<Domain.Entities.Notification> builder)
    {
        builder.Property(n => n.Title).HasMaxLength(256);
        builder.Property(n => n.Content).HasColumnType("text");
    }
}