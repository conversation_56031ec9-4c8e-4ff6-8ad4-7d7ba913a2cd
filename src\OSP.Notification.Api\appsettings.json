{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=**********;Database=demo_db;Username=postgres;Password=*********;Timeout=30;Maximum Pool Size=100;Minimum Pool Size=5"}, "Swagger": {"Authority": "https://sso.ospgroup.io.vn/realms/master", "AuthorizationUrl": "https://sso.ospgroup.io.vn/realms/master/protocol/openid-connect/auth", "TokenUrl": "https://sso.ospgroup.io.vn/realms/master/protocol/openid-connect/token", "ClientId": "notification-swagger-service", "Scope": "openid profile"}, "Email": {"Provider": "Smtp", "Smtp": {"Host": "smtp.gmail.com", "Port": 587, "UseSsl": true, "Accounts": [{"Username": "<EMAIL>", "Password": "sgvbirfvulpqnqcu"}], "DefaultFromAddress": "", "DefaultFromName": "Hoang Test Mail"}, "SendGrid": {"ApiKey": "*********************************************************************", "DefaultFromAddress": "<EMAIL>", "DefaultFromName": "Hoang Test Mail"}}, "Sms": {"Provider": "<PERSON>ee", "Stringee": {"ApiKeySid": "xxx", "ApiKeySecret": "xxx", "From": "OSP"}}, "Kafka": {"BootstrapServers": "localhost:9092"}}