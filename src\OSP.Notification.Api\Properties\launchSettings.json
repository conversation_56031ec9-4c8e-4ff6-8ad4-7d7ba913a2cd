﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "profiles": {
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": false,
      "applicationUrl": "http://localhost:3013",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "environment": "development_local",
        "project_code": "congplqg",
        "service_code": "userprofile",
        "etcd_server_hostname": "************",
        "etcd_server_port": "2379",
        "etcd_server_authentication_enabled": "false"
      }
    }
  }
}
