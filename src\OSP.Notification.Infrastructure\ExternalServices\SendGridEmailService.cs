using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;
using OSP.Notification.Domain.Config;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace OSP.Notification.Infrastructure.ExternalServices;

public class SendGridEmailService(
    IOptions<SendGridOptions> options,
    ILogger<SendGridEmailService> logger)
    : IEmailService
{
    private readonly SendGridOptions _options = options.Value;

    public async Task SendAsync(EmailMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);
        if (!message.To.Any())
        {
            throw new ArgumentException("No recipient specified in email message.");
        }

        // From
        var fromAddress = new EmailAddress(_options.DefaultFromAddress, _options.DefaultFromName);

        // To
        var tos = message.To.Select(t => new EmailAddress(t)).ToList();

        var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
            fromAddress,
            tos,
            message.Subject,
            message.IsHtml ? null : message.Body,
            message.IsHtml ? message.Body : null,
            false);

        // Cc
        foreach (var cc in message.Cc)
        {
            msg.AddCc(new EmailAddress(cc));
        }

        // Bcc
        foreach (var bcc in message.Bcc)
        {
            msg.AddBcc(new EmailAddress(bcc));
        }

        // Attachments
        foreach (var att in message.Attachments)
        {
            var base64 = Convert.ToBase64String(att.Content);
            msg.AddAttachment(att.FileName, base64, att.ContentType ?? "application/octet-stream");
        }

        var client = new SendGridClient(_options.ApiKey);

        var response = await client.SendEmailAsync(msg, cancellationToken);

        if (response.IsSuccessStatusCode)
        {
            logger.LogInformation("SendGrid email sent to {To}. Subject: {Subject}",
                string.Join(",", message.To), message.Subject);
        }
        else
        {
            var body = await response.Body.ReadAsStringAsync(cancellationToken);
            logger.LogError("SendGrid failed. StatusCode={StatusCode}, Body={Body}", response.StatusCode, body);
            throw new InvalidOperationException($"SendGrid failed. StatusCode={response.StatusCode}, Body={body}");
        }
    }

    public async Task SendBatchAsync(EmailMessage message, int batchSize = 100, int maxParallel = 5,
        CancellationToken cancellationToken = default)
    {
        var batches = message.To
            .Select((email, index) => new { email, index })
            .GroupBy(x => x.index / batchSize, x => x.email);

        using var semaphore = new SemaphoreSlim(maxParallel);

        var tasks = batches.Select(async batch =>
        {
            await semaphore.WaitAsync();
            try
            {
                var messageCopy = new EmailMessage
                {
                    To = batch,
                    Cc = message.Cc,
                    Bcc = message.Bcc,
                    Subject = message.Subject,
                    Body = message.Body,
                    IsHtml = message.IsHtml,
                    Attachments = message.Attachments
                };

                await SendAsync(messageCopy, cancellationToken);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
    }
}