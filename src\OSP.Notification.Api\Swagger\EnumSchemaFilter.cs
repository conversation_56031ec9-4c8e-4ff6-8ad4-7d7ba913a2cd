using System.ComponentModel;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace OSP.Notification.Api.Swagger;

/// <summary>
/// Filter để tùy chỉnh schema cho enum trong Swagger
/// </summary>
public class EnumSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (!context.Type.IsEnum)
        {
            return;
        }

        schema.Enum.Clear();
        var enumNames = Enum.GetNames(context.Type);
        var enumValues = Enum.GetValues(context.Type);

        schema.Type = "string";
        schema.Format = null;

        var enumDescriptions = new List<string>();

        for (var i = 0; i < enumNames.Length; i++)
        {
            var memberInfo = context.Type.GetMember(enumNames[i]).FirstOrDefault();
            var descriptionAttribute = memberInfo?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                .FirstOrDefault() as DescriptionAttribute;

            var enumValue = enumValues.GetValue(i);
            var enumDescription = descriptionAttribute?.Description ?? enumNames[i];

            schema.Enum.Add(new OpenApiString(enumNames[i]));
            if (enumValue is not null)
            {
                var enumIntValue = Convert.ToInt32(enumValue);
                enumDescriptions.Add($"• **{enumNames[i]}** ({enumIntValue}): {enumDescription}");
            }
        }

        schema.Description = $"{schema.Description}\n\n**Giá trị có thể:**\n{string.Join("\n", enumDescriptions)}";
    }
}