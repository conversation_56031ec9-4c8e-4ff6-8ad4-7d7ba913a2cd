using OSP.Common.Domain.Entities;

namespace OSP.Notification.Domain.Entities;

public class Template : BaseDomainEntity<long>, IHasCreatedBy, IHasModifiedBy
{
    public string Code { get; set; }
    public string Description { get; set; }

    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTime ModifiedAt { get; set; }
    public Guid ModifiedBy { get; set; }

    public virtual ICollection<TemplateContent> TemplateContent { get; set; }
}