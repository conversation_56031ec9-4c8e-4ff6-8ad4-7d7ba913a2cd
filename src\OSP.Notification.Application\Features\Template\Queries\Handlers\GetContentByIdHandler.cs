namespace OSP.Notification.Application.Features.Template;

public class GetContentByIdHandler(IGenericRepository<TemplateContent, long> repository)
    : IRequestHandler<GetContentByIdQuery, TemplateContentDto>
{
    public async Task<TemplateContentDto> Handle(GetContentByIdQuery request, CancellationToken cancellationToken)
    {
        var content = await repository.Query(x => x.Id == request.Id)
            .Select(x => new TemplateContentDto
            {
                Id = x.Id,
                TemplateId = x.TemplateId,
                Language = x.Language,
                Channel = x.Channel,
                Title = x.Title,
                Content = x.Content
            })
            .FirstOrDefaultAsync();

        if (content is null)
        {
            throw new EntityNotFoundException(nameof(TemplateContent), request.Id);
        }

        return (content);
    }
}