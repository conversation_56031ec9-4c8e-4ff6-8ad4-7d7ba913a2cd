using Microsoft.Extensions.Options;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;
using OSP.Notification.Domain.Config;

namespace OSP.Notification.Infrastructure.ExternalServices;

public class EmailServiceFactory(
    SmtpEmailService smtpService,
    SendGridEmailService sendGridService,
    IOptions<EmailProviderOptions> options)
    : IEmailService
{
    private readonly EmailProviderOptions _options = options.Value;

    public Task SendAsync(EmailMessage message, CancellationToken cancellationToken = default)
    {
        return _options.Provider switch
        {
            "Smtp" => smtpService.SendAsync(message, cancellationToken),
            "SendGrid" => sendGridService.SendAsync(message, cancellationToken),
            _ => throw new InvalidOperationException($"Unknown email provider: {_options.Provider}")
        };
    }

    public Task SendBatchAsync(EmailMessage message, int batchSize = 100, int maxParallel = 5,
        CancellationToken cancellationToken = default)
    {
        return _options.Provider switch
        {
            "Smtp" => smtpService.SendBatchAsync(message, batchSize, maxParallel, cancellationToken),
            "SendGrid" => sendGridService.SendBatchAsync(message, batchSize, maxParallel, cancellationToken),
            _ => throw new InvalidOperationException($"Unknown email provider: {_options.Provider}")
        };
    }
}