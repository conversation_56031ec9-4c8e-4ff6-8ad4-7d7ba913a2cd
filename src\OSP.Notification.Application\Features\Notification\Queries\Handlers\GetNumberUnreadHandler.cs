using OSP.Common.Domain.Enums;

namespace OSP.Notification.Application.Features.Notification;

public class GetNumberUnreadHandler(IGenericRepository<UserNotification, Guid> repository)
    : IRequestHandler<GetNumberUnreadQuery, int>
{
    public Task<int> Handle(GetNumberUnreadQuery request, CancellationToken cancellationToken)
    {
        return repository.CountAsync(x
            => x.UserId == request.UserId
               && !x.IsRead
               && x.Channel == NotificationChannel.Internal
               && x.Status == NotificationStatus.Sent);
    }
}