using OSP.Common.Domain.Enums;

namespace OSP.Notification.Application.Features.Notification;

public class GetByIdHandler(IGenericRepository<UserNotification, Guid> repository)
    : IRequestHandler<GetByIdQuery, NotificationDto>
{
    public async Task<NotificationDto> Handle(GetByIdQuery request, CancellationToken cancellationToken)
    {
        var notification = await repository.Query(x
                => x.UserId == request.UserId
                   && x.Notification.Id == request.Id
                   && x.Channel == NotificationChannel.Internal
                   && x.Status == NotificationStatus.Sent)
            .OrderByDescending(x => x.CreatedAt)
            .Select(x
                => new NotificationDto
                {
                    Id = x.Notification.Id,
                    Title = x.Notification.Title!,
                    Content = x.Notification.Content,
                    SentAt = x.SentAt!.Value,
                    IsRead = x.IsRead,
                    ReadAt = x.ReadAt
                })
            .FirstOrDefaultAsync();

        if (notification is null)
        {
            throw new EntityNotFoundException(nameof(Notification), request.Id);
        }

        return notification;
    }
}