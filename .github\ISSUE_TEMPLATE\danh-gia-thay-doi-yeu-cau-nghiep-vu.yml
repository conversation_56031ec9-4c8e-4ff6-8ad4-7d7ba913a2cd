name: "<PERSON><PERSON><PERSON> gi<PERSON> thay đổi yêu cầu nghiệp vụ"
description: "Template đ<PERSON> đánh giá tác động khi có thay đổi trong tài liệu yêu cầu nghiệp vụ"
title: "[REVIEW] <PERSON><PERSON><PERSON> giá thay đổi yêu cầu nghiệp vụ - {{CHANGE_DATE}}"
labels: ["requirements-review", "business-analysis"]
assignees: []
body:
  - type: markdown
    attributes:
      value: |
        ## 📋 Thông tin thay đổi
        
        **Thời gian phát hiện:** {{DETECTION_TIME}}
        **Người commit:** {{COMMITTER}}
        
  - type: textarea
    id: content-changes
    attributes:
      label: "Nội dung thay đổi"
      description: "Chi tiết về những thay đổi trong tài liệu yêu cầu"
      value: |
        **Commit trước:** {{PREVIOUS_COMMIT}} ([Xem chi tiết]({{PREVIOUS_COMMIT_LINK}}))
        **Commit sau:** {{CURRENT_COMMIT}} ([Xem chi tiết]({{CURRENT_COMMIT_LINK}}))
        
        ### Tệp tin bị thay đổi:
        {{CHANGED_FILES}}
        
        ### Chi tiết thay đổi:
        {{DIFF_CONTENT}}
    validations:
      required: true

  - type: dropdown
    id: change-type
    attributes:
      label: "Loại thay đổi"
      description: "Phân loại loại thay đổi dựa trên nội dung"
      options:
        - "Tính năng mới"
        - "Sửa đổi tính năng cũ"
        - "Chưa phân loại"
    validations:
      required: true

  - type: textarea
    id: impact-analysis
    attributes:
      label: "Phân tích tác động"
      description: "Thay đổi này sẽ ảnh hưởng đến những user story/epic nào?"
      placeholder: |
        Ví dụ:
        - Epic: Quản lý người dùng (US-001, US-002)
        - User Story: Đăng nhập hệ thống (US-010)
        - User Story: Phân quyền người dùng (US-015)
    validations:
      required: true

  - type: checkboxes
    id: role-actions
    attributes:
      label: "Công việc cần thực hiện theo vai trò"
      description: "Thay đổi này cần các role phải làm thêm những gì?"
      options:
        - label: "**Designer**: Cập nhật UI/UX design, wireframe, prototype"
        - label: "**System Analyst (SA)**: Cập nhật tài liệu phân tích, use case, business rules"
        - label: "**Developer**: Cập nhật code, API, database schema"
        - label: "**Tester**: Cập nhật test case, test plan, automation test"

  - type: textarea
    id: designer-tasks
    attributes:
      label: "Công việc chi tiết cho Designer"
      description: "Mô tả cụ thể công việc Designer cần thực hiện"
      placeholder: |
        Ví dụ:
        - Thiết kế lại màn hình đăng nhập
        - Cập nhật component library
        - Tạo prototype cho tính năng mới

  - type: textarea
    id: sa-tasks
    attributes:
      label: "Công việc chi tiết cho System Analyst"
      description: "Mô tả cụ thể công việc SA cần thực hiện"
      placeholder: |
        Ví dụ:
        - Cập nhật tài liệu phân tích hệ thống
        - Xem xét lại use case diagram
        - Cập nhật business rules

  - type: textarea
    id: dev-tasks
    attributes:
      label: "Công việc chi tiết cho Developer"
      description: "Mô tả cụ thể công việc Developer cần thực hiện"
      placeholder: |
        Ví dụ:
        - Cập nhật API authentication
        - Thay đổi database schema
        - Implement tính năng mới

  - type: textarea
    id: tester-tasks
    attributes:
      label: "Công việc chi tiết cho Tester"
      description: "Mô tả cụ thể công việc Tester cần thực hiện"
      placeholder: |
        Ví dụ:
        - Cập nhật test cases cho tính năng đăng nhập
        - Viết automation test mới
        - Thực hiện regression testing

  - type: dropdown
    id: priority
    attributes:
      label: "Mức độ ưu tiên"
      description: "Đánh giá mức độ ưu tiên của việc xử lý thay đổi này"
      options:
        - "Thấp"
        - "Trung bình"
        - "Cao"
        - "Khẩn cấp"
    validations:
      required: true

  - type: textarea
    id: next-steps
    attributes:
      label: "Bước tiếp theo"
      description: "Kế hoạch xử lý và timeline dự kiến"
      placeholder: |
        Ví dụ:
        - [ ] Schedule meeting với stakeholders (Date: DD/MM/YYYY)
        - [ ] Review và approve design changes (Date: DD/MM/YYYY)
        - [ ] Cập nhật development plan (Date: DD/MM/YYYY)
        - [ ] Thực hiện testing (Date: DD/MM/YYYY)

  - type: textarea
    id: additional-notes
    attributes:
      label: "Ghi chú bổ sung"
      description: "Thông tin bổ sung hoặc các rủi ro cần lưu ý"
      placeholder: "Các thông tin khác cần lưu ý..."
