using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace OSP.Notification.Infrastructure.EntityConfig;

public class TemplateConfig : IEntityTypeConfiguration<Template>
{
    public void Configure(EntityTypeBuilder<Template> builder)
    {
        builder.Property(x => x.Code).HasMaxLength(128);
        builder.Property(x => x.Description).HasMaxLength(256);

        builder.HasIndex(x => x.Code).IsUnique();
    }
}