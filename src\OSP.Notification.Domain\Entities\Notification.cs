using OSP.Common.Domain.Entities;

namespace OSP.Notification.Domain.Entities;

public class Notification : BaseDomainEntity<Guid>, IHasCreatedBy, IHasModifiedBy
{
    public string? Title { get; set; }
    public string Content { get; set; }

    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTime ModifiedAt { get; set; }
    public Guid ModifiedBy { get; set; }

    public virtual ICollection<UserNotification> UserNotification { get; set; } = new List<UserNotification>();
}