namespace OSP.Notification.Application.Features.Template;

public class DeleteTemplateHandler(IGenericRepository<Domain.Entities.Template, long> repository)
    : IRequestHandler<DeleteTemplateCommand, bool>
{
    public async Task<bool> Handle(DeleteTemplateCommand request, CancellationToken cancellationToken)
    {
        return await repository
            .Query(x => x.Id == request.Id)
            .ExecuteDeleteAsync() > 0;
    }
}