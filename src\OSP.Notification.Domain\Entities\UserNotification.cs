using OSP.Common.Domain.Entities;
using OSP.Common.Domain.Enums;

namespace OSP.Notification.Domain.Entities;

public class UserNotification : BaseDomainEntity<Guid>, IHasCreatedBy, IHasModifiedBy
{
    public Guid? UserId { get; set; }
    public string? ExternalReceiver { get; set; }
    public NotificationChannel Channel { get; set; }
    public NotificationStatus Status { get; set; }
    public DateTime? SentAt { get; set; }
    public string? ErrorMessage { get; set; }
    public bool IsRead { get; set; } = false;
    public DateTime? ReadAt { get; set; }

    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTime ModifiedAt { get; set; }
    public Guid ModifiedBy { get; set; }

    public Guid NotificationId { get; set; }
    public virtual Notification Notification { get; set; }
}