using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OSP.Common.Api.Controllers;
using OSP.Common.Application.Contexts;
using OSP.Common.Application.ExternalServices;
using OSP.Notification.Application.Features.Notification;
using OSP.Notification.Application.Models;

namespace OSP.Notification.Api.Controllers;

[ApiVersion(1)]
[AllowAnonymous]
public class NotificationController(
    IMediator mediator,
    IRequestContext context,
    INotificationGatewayService notificationGatewayService
) : BaseRestA<PERSON>(mediator)
{
    private readonly IRequestContext _context = context;

    /// <summary>
    /// Lấy thông tin notification theo ID
    /// </summary>
    /// <param name="id">ID của notification</param>
    /// <returns>Thông tin notification</returns>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<NotificationDto>> GetById(Guid id)
    {
        var result = await mediator.Send(new GetByIdQuery(id, _context.UserId));
        return Ok(result);
    }

    /// <summary>
    /// L<PERSON>y tất cả notifications của user
    /// </summary>
    /// <returns>Danh sách notifications</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<NotificationDto>>> GetAll()
    {
        var result = await mediator.Send(new GetAllQuery(_context.UserId));
        return Ok(result);
    }

    /// <summary>
    /// Lấy danh sách notifications chưa đọc của user
    /// </summary>
    /// <returns>Danh sách notifications chưa đọc</returns>
    [HttpGet("Unread")]
    public async Task<ActionResult<IEnumerable<NotificationDto>>> GetUnread()
    {
        var result = await mediator.Send(new GetUnreadQuery(_context.UserId));
        return Ok(result);
    }

    /// <summary>
    /// Lấy số lượng notifications chưa đọc của user
    /// </summary>
    /// <returns>Số lượng notifications chưa đọc</returns>
    [HttpGet("Unread/Count")]
    public async Task<ActionResult<int>> GetNumberUnread()
    {
        var result = await mediator.Send(new GetNumberUnreadQuery(_context.UserId));
        return Ok(result);
    }

    /// <summary>
    /// Tạo notification mới
    /// </summary>
    /// <param name="command">Thông tin tạo notification</param>
    /// <returns>Danh sách ID notifications đã tạo</returns>
    [HttpPost]
    public async Task<ActionResult<IEnumerable<Guid>>> Create([FromBody] CreateNotificationCommand command)
    {
        // await notificationGatewayService.SendAsync(command);
        // return Ok();
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Đánh dấu notification đã đọc/chưa đọc
    /// </summary>
    /// <param name="id">ID của notification</param>
    /// <param name="command">Thông tin đánh dấu đọc</param>
    /// <returns>Kết quả thành công</returns>
    [HttpPatch("{id:guid}/MarkRead")]
    public async Task<ActionResult> MarkRead(Guid id, [FromBody] MarkReadCommand command)
    {
        command.NotificationId = id;
        command.UserId = _context.UserId;
        await mediator.Send(command);
        return Ok();
    }

    /// <summary>
    /// Đánh dấu tất cả notifications đã đọc
    /// </summary>
    /// <returns>Kết quả thành công</returns>
    [HttpPatch("MarkAllRead")]
    public async Task<ActionResult> MarkAllRead()
    {
        var command = new MarkAllReadCommand { UserId = _context.UserId };
        await mediator.Send(command);
        return Ok();
    }
}