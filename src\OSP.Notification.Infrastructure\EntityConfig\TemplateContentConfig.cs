using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace OSP.Notification.Infrastructure.EntityConfig;

public class TemplateContentConfig : IEntityTypeConfiguration<TemplateContent>
{
    public void Configure(EntityTypeBuilder<TemplateContent> builder)
    {
        builder.Property(x => x.Language).HasMaxLength(8).HasDefaultValue("vi");
        builder.Property(x => x.Title).HasMaxLength(256);
        builder.Property(x => x.Content).HasColumnType("text");

        builder.HasIndex(x => new { x.TemplateId, x.Language }).IsUnique();

        builder.HasOne(x => x.Template)
            .WithMany(n => n.TemplateContent)
            .HasForeignKey(x => x.TemplateId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}