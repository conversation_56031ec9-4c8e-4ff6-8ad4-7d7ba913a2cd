using OSP.Common.Domain.Enums;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Strategies.NotificationStrategy;
using SmartFormat;

namespace OSP.Notification.Application.Features.Notification;

public class CreateNotificationHandler(
    IGenericRepository<Domain.Entities.Notification, Guid> notificationRepository,
    IGenericRepository<UserNotification, Guid> userNotificationRepository,
    IGenericRepository<TemplateContent, long> templateRepository,
    IEnumerable<INotificationStrategy> strategies,
    IUserService userService,
    DbContext dbContext
) : IRequestHandler<CreateNotificationCommand, IEnumerable<Guid>>
{
    public async Task<IEnumerable<Guid>> Handle(CreateNotificationCommand request, CancellationToken cancellationToken)
    {
        var notificationMessages = new List<Domain.Entities.Notification>();

        var recipients = await userService.GetUserNotificationSettingsAsync(request.Recipient.UserIds);

        foreach (var channel in request.Channels)
        {
            var strategy = strategies.FirstOrDefault(s => s.Channel == channel);
            if (strategy is null)
            {
                continue;
            }

            var title = request.Title;
            var content = request.Content;


            // Nếu có template code, lấy template và render nội dung
            if (request.TemplateCode.IsNotEmpty())
            {
                if (request.Language.IsEmpty())
                {
                    request.Language = "vi"; // Mặc định tiếng Việt
                }

                var template = await templateRepository
                    .Query(x => x.Template.Code == request.TemplateCode
                                && x.Language == request.Language
                                && x.Channel == channel)
                    .FirstOrDefaultAsync();

                if (template is not null)
                {
                    title = template.Title.IsNotEmpty() ? Smart.Format(template.Title!, request.Data) : template.Title;
                    content = Smart.Format(template.Content, request.Data);
                }
            }

            if (content.IsEmpty())
            {
                continue;
            }

            var message = new Domain.Entities.Notification
            {
                Title = title,
                Content = content!,
                CreatedBy = request.SenderId,
                ModifiedBy = request.SenderId,
            };
            notificationMessages.Add(message);
            var messageId = await notificationRepository.InsertAsync(message);

            // Lọc ra các người nhận hợp lệ cho kênh này
            var recipientsForChannel = recipients
                .Where(u => u.Preferences.Any(p => p.Channel == channel && p.IsEnabled))
                .ToList();

            // Ghi log và gửi thông báo
            var notificationRecords = recipientsForChannel
                .Select(user => new UserNotification
                {
                    NotificationId = messageId,
                    UserId = user.Id,
                    Channel = channel,
                    Status = NotificationStatus.Pending,
                    CreatedBy = request.SenderId,
                    ModifiedBy = request.SenderId,
                })
                .ToList();
            notificationRecords.AddRange(request.Recipient.Emails
                .Where(e => e.IsNotEmpty())
                .Distinct()
                .Select(email => new UserNotification
                {
                    NotificationId = messageId,
                    ExternalReceiver = email,
                    Channel = channel,
                    Status = NotificationStatus.Pending,
                    CreatedBy = request.SenderId,
                    ModifiedBy = request.SenderId,
                }));
            notificationRecords.AddRange(request.Recipient.PhoneNumbers
                .Where(p => p.IsNotEmpty())
                .Distinct()
                .Select(phoneNumber => new UserNotification
                {
                    NotificationId = messageId,
                    ExternalReceiver = phoneNumber,
                    Channel = channel,
                    Status = NotificationStatus.Pending,
                    CreatedBy = request.SenderId,
                    ModifiedBy = request.SenderId,
                }));

            await userNotificationRepository.InsertRangeAsync(notificationRecords);

            // Lấy thông tin người nhận
            var userIds = recipientsForChannel
                .Select(x => x.Id)
                .Distinct()
                .ToList();
            var emails = request.Recipient.Emails
                .Concat(recipientsForChannel.Where(x => x.Email.IsNotEmpty()).Select(x => x.Email))
                .Distinct()
                .ToList();
            var phoneNumbers = request.Recipient.PhoneNumbers
                .Concat(recipientsForChannel.Where(x => x.PhoneNumber.IsNotEmpty()).Select(x => x.PhoneNumber!))
                .Distinct()
                .ToList();

            var payload = new NotificationPayload
            {
                Id = messageId,
                Title = message.Title,
                Content = message.Content,
                UserIds = userIds,
                Emails = emails,
                PhoneNumbers = phoneNumbers,
                Data = request.Data
            };
            try
            {
                await strategy.SendAsync(payload);

                foreach (var n in notificationRecords)
                {
                    n.Status = NotificationStatus.Sent;
                    n.SentAt = DateTime.Now;
                }
            }
            catch (Exception e)
            {
                foreach (var n in notificationRecords)
                {
                    n.Status = NotificationStatus.Failed;
                    n.ErrorMessage = e.Message;
                }
            }
            finally
            {
                await dbContext.SaveChangesAsync(); // Cập nhật trạng thái
            }
        }

        return notificationMessages.Select(x => x.Id);
    }
}