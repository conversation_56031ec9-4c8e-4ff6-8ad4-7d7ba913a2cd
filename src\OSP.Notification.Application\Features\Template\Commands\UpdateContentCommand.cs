using OSP.Common.Domain.Enums;

namespace OSP.Notification.Application.Features.Template;

public class UpdateContentCommand : IRequest<bool>
{
    public long Id { get; set; }
    public NotificationChannel Channel { get; set; }
    public string Language { get; set; } = "vi";
    public string? Title { get; set; }
    public string Content { get; set; }
}

public class UpdateContentValidator : AbstractValidator<UpdateContentCommand>
{
    public UpdateContentValidator()
    {
        RuleFor(x => x.Id).GreaterThan(0);
        RuleFor(x => x.Language).NotEmpty();
        RuleFor(x => x.Content).NotEmpty();
    }
}