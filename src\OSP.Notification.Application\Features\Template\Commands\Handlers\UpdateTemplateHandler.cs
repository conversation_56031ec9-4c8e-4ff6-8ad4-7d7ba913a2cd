using OSP.Common.Application.Contexts;

namespace OSP.Notification.Application.Features.Template;

public class UpdateTemplateHandler(
    IGenericRepository<Domain.Entities.Template, long> repository,
    IRequestContext requestContext)
    : IRequestHandler<UpdateTemplateCommand, bool>
{
    public async Task<bool> Handle(UpdateTemplateCommand request, CancellationToken cancellationToken)
    {
        var now = DateTime.Now;
        return await repository
            .Query(x => x.Id == request.Id)
            .ExecuteUpdateAsync(x
                => x.SetProperty(x => x.Code, request.Code)
                    .SetProperty(x => x.Description, request.Description)
                    .SetProperty(p => p.ModifiedAt, now)
                    .SetProperty(p => p.ModifiedBy, requestContext.UserId)
            ) > 0;
    }
}