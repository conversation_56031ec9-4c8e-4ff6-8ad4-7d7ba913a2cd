﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>OSP.Notification.Infrastructure</RootNamespace>
    </PropertyGroup>

    <PropertyGroup>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MailKit" Version="4.13.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="SendGrid" Version="9.29.3"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\osp-common-be-dotnet\src\OSP.Common.Application\OSP.Common.Application.csproj"/>
        <ProjectReference Include="..\..\..\osp-common-be-dotnet\src\OSP.Common.Domain\OSP.Common.Domain.csproj"/>
        <ProjectReference Include="..\..\..\osp-common-be-dotnet\src\OSP.Common.Infrastructure\OSP.Common.Infrastructure.csproj"/>
        <ProjectReference Include="..\OSP.Notification.Application\OSP.Notification.Application.csproj"/>
        <ProjectReference Include="..\OSP.Notification.Domain\OSP.Notification.Domain.csproj"/>
    </ItemGroup>

</Project>
