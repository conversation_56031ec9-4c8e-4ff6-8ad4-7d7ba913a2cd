namespace OSP.Notification.Domain.Config;

public class SmtpOptions
{
    public string Host { get; set; }
    public int Port { get; set; }
    public bool UseSsl { get; set; }
    public IEnumerable<SmtpAccount> Accounts { get; set; } = [];
    public string DefaultFromAddress { get; set; }
    public string DefaultFromName { get; set; }
}

public class SmtpAccount
{
    public string UserName { get; set; }
    public string Password { get; set; }
}