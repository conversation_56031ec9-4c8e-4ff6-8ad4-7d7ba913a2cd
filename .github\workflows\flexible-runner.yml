name: Flexible Runner Workflow

on:
  # <PERSON> phép chạy workflow thủ công từ tab Actions
  workflow_dispatch:
    inputs:
      runner_choice:
        description: 'Chọn runner để chạy job'
        required: true
        type: choice
        # Sử dụng trực tiếp nhãn runner thật để đơn giản hóa
        options:
          - ubuntu-latest
          - mac-self-hosted
          - linux-self-hosted
        default: 'ubuntu-latest'
      job_type:
        description: 'Loại job cần chạy'
        required: false
        type: choice
        options:
          - build-only
          - build-with-dotnet
        default: 'build-only'

  # Ví dụ: cũng có thể trigger khi push vào branch main
  push:
    branches:
      - main

jobs:
  # Job sử dụng reusable workflow
  flexible-runner-job:
    name: Flexible Runner Demo
    uses: ./.github/workflows/reusable-flexible-runner.yml
    with:
      runner_choice: ${{ github.event.inputs.runner_choice || vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
      job_name: 'Build on Selected Runner'
      setup_dotnet: ${{ github.event.inputs.job_type == 'build-with-dotnet' }}
      install_dependencies: ${{ github.event.inputs.job_type == 'build-with-dotnet' && 'backend' || 'none' }}
      run_command: |
        echo "🚀 Running flexible runner demo..."
        echo "Job type: ${{ github.event.inputs.job_type || 'build-only' }}"
        echo "Selected runner: ${{ github.event.inputs.runner_choice || vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}"
        echo "✅ Demo completed successfully!"
