using Microsoft.Extensions.Options;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Domain.Config;

namespace OSP.Notification.Infrastructure.ExternalServices;

public class RoundRobinEmailService(IOptions<SmtpOptions> options) : IRoundRobinEmailService
{
    private readonly SmtpOptions _options = options.Value;
    private int _accountIndex = -1; // dùng cho round-robin

    public SmtpAccount GetAccount()
    {
        if (!_options.Accounts.Any())
        {
            throw new InvalidOperationException("No Accounts configured in message or SmtpOptions.");
        }

        if (_options.Accounts.Count() == 1)
        {
            return _options.Accounts.First();
        }

        // Round-robin
        var accounts = _options.Accounts.ToList();
        var index = Interlocked.Increment(ref _accountIndex);
        return accounts[index % accounts.Count];
    }
}