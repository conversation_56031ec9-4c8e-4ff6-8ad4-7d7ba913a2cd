using OSP.Common.Domain.Enums;

namespace OSP.Notification.Application.Features.Template;

public record GetContentByCodeQuery(string Code, NotificationChannel Channel, string Language = "vi")
    : IRequest<TemplateContentDto>;

public class GetContentByCodeValidator : AbstractValidator<GetContentByCodeQuery>
{
    public GetContentByCodeValidator()
    {
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.Language).NotEmpty();
    }
}