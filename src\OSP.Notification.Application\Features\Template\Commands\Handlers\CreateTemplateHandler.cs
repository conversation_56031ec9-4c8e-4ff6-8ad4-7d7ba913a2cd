namespace OSP.Notification.Application.Features.Template;

public class CreateTemplateHandler(IGenericRepository<Domain.Entities.Template, long> repository)
    : IRequestHandler<CreateTemplateCommand, long>
{
    public async Task<long> Handle(CreateTemplateCommand request, CancellationToken cancellationToken)
    {
        var template = new Domain.Entities.Template
        {
            Code = request.Code,
            Description = request.Description
        };

        await repository.InsertAsync(template);
        return template.Id;
    }
}