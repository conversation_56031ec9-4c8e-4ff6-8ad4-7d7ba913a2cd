using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using OSP.Common.Domain.Enums;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;

namespace OSP.Notification.Infrastructure.ExternalServices;

public class UserService(HttpClient httpClient, ILogger<UserService> logger) : IUserService
{
    public async Task<IEnumerable<User>> GetUserNotificationSettingsAsync(IEnumerable<Guid> userIds)
    {
        if (!userIds.Any())
        {
            return [];
        }

        try
        {
            using var response = await httpClient.PostAsJsonAsync("/api/v1/users/GetUserNotificationSettings", userIds);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadFromJsonAsync<IEnumerable<User>>() ?? [];
        }
        catch (Exception e)
        {
            logger.LogError("Failed to fetch user notification settings. UserIds={UserIds}, Error={Error}",
                string.Join(",", userIds), e.Message);
            return
            [
                new User
                {
                    Id = new Guid("dec3dacf-4632-432b-985b-9ff3c207b179"),
                    Email = "<EMAIL>",
                    PhoneNumber = "0365195599",
                    Preferences =
                    [
                        new UserNotificationPreference
                        {
                            Channel = NotificationChannel.Email,
                            IsEnabled = true
                        },
                        new UserNotificationPreference
                        {
                            Channel = NotificationChannel.SMS,
                            IsEnabled = true
                        },
                        new UserNotificationPreference
                        {
                            Channel = NotificationChannel.Internal,
                            IsEnabled = true
                        },
                        new UserNotificationPreference
                        {
                            Channel = NotificationChannel.Zalo,
                            IsEnabled = true
                        }
                    ]
                }
            ];
        }
    }
}