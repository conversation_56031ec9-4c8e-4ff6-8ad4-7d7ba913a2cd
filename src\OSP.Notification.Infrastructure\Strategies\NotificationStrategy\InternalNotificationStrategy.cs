using Microsoft.AspNetCore.SignalR;
using OSP.Common.Domain.Enums;
using OSP.Notification.Application.Models;
using OSP.Notification.Application.Strategies.NotificationStrategy;

namespace OSP.Notification.Infrastructure.Strategies.NotificationStrategy;

public class InternalNotificationStrategy(IHubContext<NotificationHub> hubContext) : INotificationStrategy
{
    public NotificationChannel Channel => NotificationChannel.Internal;

    public Task SendAsync(NotificationPayload payload)
    {
        if (!payload.UserIds.Any())
        {
            return Task.CompletedTask;
        }

        return hubContext.Clients.Users(payload.UserIds.Select(x => x.ToString())).SendAsync("NewNotification", new
        {
            payload.Id
        });
    }
}