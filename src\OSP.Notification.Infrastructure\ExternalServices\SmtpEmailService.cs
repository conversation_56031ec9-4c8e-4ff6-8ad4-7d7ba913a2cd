using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MimeKit;
using OSP.Common.Domain.Extensions;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;
using OSP.Notification.Domain.Config;

namespace OSP.Notification.Infrastructure.ExternalServices;

public class SmtpEmailService(
    IOptions<SmtpOptions> options,
    ILogger<SmtpEmailService> logger,
    IRoundRobinEmailService roundRobinEmailService)
    : IEmailService
{
    private readonly SmtpOptions _options = options.Value;

    public async Task SendAsync(EmailMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);
        if (!message.To.Any())
        {
            throw new ArgumentException("No recipient specified in email message.");
        }

        using var client = new SmtpClient();

        try
        {
            // Connect
            var secureSocketOptions = _options.UseSsl ? SecureSocketOptions.StartTls : SecureSocketOptions.Auto;
            await client.ConnectAsync(_options.Host, _options.Port, secureSocketOptions, cancellationToken);

            // Authenticate if provided
            var account = roundRobinEmailService.GetAccount();
            if (account.UserName.IsNotEmpty())
            {
                await client.AuthenticateAsync(account.UserName, account.Password, cancellationToken);
            }

            var mime = BuildMimeMessage(message, account);
            await client.SendAsync(mime, cancellationToken);

            logger.LogInformation("Email sent to {To}. Subject: {Subject}",
                string.Join(",", message.To), message.Subject);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to send email to {To}. Subject: {Subject}",
                string.Join(",", message.To), message.Subject);
            throw; // bubble up so caller can handle retries if needed
        }
        finally
        {
            try
            {
                await client.DisconnectAsync(true, cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Error during SMTP disconnect");
            }
        }
    }

    public async Task SendBatchAsync(EmailMessage message, int batchSize = 100, int maxParallel = 5,
        CancellationToken cancellationToken = default)
    {
        var batches = message.To
            .Select((email, index) => new { email, index })
            .GroupBy(x => x.index / batchSize, x => x.email);

        using var semaphore = new SemaphoreSlim(maxParallel);

        var tasks = batches.Select(async batch =>
        {
            await semaphore.WaitAsync();
            try
            {
                var messageCopy = new EmailMessage
                {
                    To = batch,
                    Cc = message.Cc,
                    Bcc = message.Bcc,
                    Subject = message.Subject,
                    Body = message.Body,
                    IsHtml = message.IsHtml,
                    Attachments = message.Attachments
                };

                await SendAsync(messageCopy, cancellationToken);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
    }

    private MimeMessage BuildMimeMessage(EmailMessage msg, SmtpAccount account)
    {
        var mime = new MimeMessage();

        // From
        mime.From.Add(new MailboxAddress(_options.DefaultFromName, account.UserName));

        AddList(msg.To, mime.To);
        AddList(msg.Cc, mime.Cc);
        AddList(msg.Bcc, mime.Bcc);

        mime.Subject = msg.Subject;

        // Body + attachments
        var bodyBuilder = new BodyBuilder();
        if (msg.IsHtml)
        {
            bodyBuilder.HtmlBody = msg.Body;
        }
        else
        {
            bodyBuilder.TextBody = msg.Body;
        }

        foreach (var att in msg.Attachments)
        {
            if (att.Content.Length > 0)
            {
                bodyBuilder.Attachments.Add(att.FileName, att.Content,
                    ContentType.Parse(att.ContentType ?? "application/octet-stream"));
            }
        }

        mime.Body = bodyBuilder.ToMessageBody();

        return mime;

        // To / Cc / Bcc
        void AddList(IEnumerable<string> list, InternetAddressList target)
        {
            foreach (var addr in list)
            {
                target.Add(new MailboxAddress(string.Empty, addr));
            }
        }
    }
}