name: Pull Request Notification

on:
  pull_request:
    types: [opened, ready_for_review]

concurrency:
  group: pr-notification-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  notify-lark:
    name: Lark Pull Request Notification
    uses: ./.github/workflows/reusable-lark-notification.yml
    # Chỉ trigger khi PR không phải draft
    if: github.event.pull_request.draft == false
    with:
      notification_type: 'pr'
      title: ${{ github.event.pull_request.title }}
      content: 'auto'
      event_url: ${{ github.event.pull_request.html_url }}
      event_number: ${{ github.event.pull_request.number }}
      event_user: ${{ github.event.pull_request.user.login }}
      event_created_at: ${{ github.event.pull_request.created_at }}
      additional_info: |
        {
          "base_branch": "${{ github.event.pull_request.base.ref }}",
          "head_branch": "${{ github.event.pull_request.head.ref }}",
          "status": "Cần review"
        }
      header_template: 'blue'
    secrets:
      LARK_CHAT_GROUP_NOTIFICATION: ${{ secrets.LARK_CHAT_GROUP_NOTIFICATION }}
