namespace OSP.Notification.Application.ExternalServices;

public interface IEmailService
{
    /// <summary>
    ///     G<PERSON>i email bất đồng bộ.
    /// </summary>
    Task SendAsync(EmailMessage message, CancellationToken cancellationToken = default);

    Task SendBatchAsync(EmailMessage message, int batchSize = 100, int maxParallel = 5,
        CancellationToken cancellationToken = default);
}