name: "<PERSON><PERSON><PERSON> bướ<PERSON> thiết lập cho Copilot"

# Tự động chạy các setup steps khi có thay đổi để dễ dàng validation, và
# cho phép manual testing thông qua tab "Actions" của repository
on:
  workflow_dispatch:
  push:
    paths:
      - .github/workflows/copilot-setup-steps.yml
  pull_request:
    paths:
      - .github/workflows/copilot-setup-steps.yml

jobs:
  # Job BẮT BUỘC phải có tên `copilot-setup-steps` nếu không Copilot sẽ không nhận diện được.
  copilot-setup-steps:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: <PERSON><PERSON><PERSON> tra mã nguồn
        uses: actions/checkout@v4
      - name: Thiết lập .NET 9
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0"
      - name: Cache NuGet packages
        uses: actions/cache@v4
        with:
          path: |
            ~/.nuget/packages
            ~/.local/share/NuGet/Cache
            ~/AppData/Local/NuGet/Cache
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/*.fsproj', '**/*.vbproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-
      - name: Kiểm tra phiên bản môi trường
        run: |
          echo "=== Environment Versions ==="
          echo ".NET version:"
          dotnet --version
          echo ".NET info:"
          dotnet --info
          echo "============================="
      - name: Cài đặt dependencies .NET
        run: |
          echo "📦 Installing .NET Backend dependencies..."
          dotnet restore
          echo "✅ .NET Backend dependencies installed successfully!"
        if: hashFiles('**/*.csproj', '**/*.sln') != ''
