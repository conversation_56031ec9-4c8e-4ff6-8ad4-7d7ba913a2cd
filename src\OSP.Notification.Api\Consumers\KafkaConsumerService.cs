using System.Text.Json;
using Confluent.Kafka;
using Microsoft.Extensions.Options;
using OSP.Common.Domain.Config;
using OSP.Common.Infrastructure.Constants;
using OSP.Notification.Application.Features.Notification;

namespace OSP.Notification.Api.Consumers;

public class KafkaConsumerService(
    ILogger<KafkaConsumerService> logger,
    IServiceScopeFactory scopeFactory,
    IOptions<KafkaSettings> options)
    : BackgroundService
{
    private readonly KafkaSettings _settings = options.Value;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var config = new ConsumerConfig
        {
            // 1. Broker
            BootstrapServers = _settings.BootstrapServers,

            // 2. Consumer Group (mỗi service instance cùng GroupId sẽ chia nhau message)
            GroupId = "notification-service-group",

            // 3. Offset
            EnableAutoCommit = false, // Best practice: tự commit sau khi xử lý xong
            AutoOffsetReset = AutoOffsetReset.Earliest, // Nếu chưa có offset -> đọ<PERSON> từ đầu topic
            EnablePartitionEof = false, // Không cần event EOF mỗi partition

            // 4. Performance tuning
            FetchMinBytes = 1, // Đọc ngay khi có message
            FetchMaxBytes = 50 * 1024 * 1024, // 50MB max fetch (tăng nếu batch lớn)
            MaxPartitionFetchBytes = 5 * 1024 * 1024, // 5MB/partition (tùy payload notification)
            QueuedMinMessages = 1000, // Giữ queue nhỏ để poll liên tục
            SessionTimeoutMs = 10000, // Timeout cho heartbeat (10s)
            MaxPollIntervalMs = 300000, // Max time xử lý 1 batch (5 phút)

            // 5. Debug & monitoring
            ClientId = "NotificationConsumer",
            StatisticsIntervalMs = 60000, // Emit stats 60s/lần
            EnableAutoOffsetStore = false, // Chủ động store offset sau khi xử lý thành công
        };

        using var consumer = new ConsumerBuilder<Ignore, string>(config)
            .SetErrorHandler((_, e) => logger.LogError("Kafka error: {Reason}", e.Reason))
            .Build();

        consumer.Subscribe(KafkaTopic.NOTIFICATION);

        logger.LogInformation("Kafka consumer started, listening on topic {Topic}", KafkaTopic.NOTIFICATION);

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var result = consumer.Consume(stoppingToken);

                    logger.LogInformation("Received message: {Message}", result.Message.Value);

                    // Deserialize command
                    var command = JsonSerializer.Deserialize<CreateNotificationCommand>(
                        result.Message.Value);

                    if (command is not null)
                    {
                        using var scope = scopeFactory.CreateScope();
                        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

                        await mediator.Send(command, stoppingToken);

                        // Commit offset only after success
                        consumer.StoreOffset(result);
                    }
                }
                catch (ConsumeException ex)
                {
                    logger.LogError(ex, "Kafka consume error");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error processing Kafka message");
                }
            }
        }
        finally
        {
            consumer.Close();
        }
    }
}