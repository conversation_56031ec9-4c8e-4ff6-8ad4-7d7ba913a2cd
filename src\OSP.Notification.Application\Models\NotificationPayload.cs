namespace OSP.Notification.Application.Models;

public class NotificationPayload
{
    public Guid Id { get; set; }
    public IEnumerable<Guid> UserIds { get; set; } = [];
    public IEnumerable<string> Emails { get; set; } = [];
    public IEnumerable<string> PhoneNumbers { get; set; } = [];
    public string? Title { get; set; }
    public string Content { get; set; }
    public Dictionary<string, string> Data { get; set; } = new();
}