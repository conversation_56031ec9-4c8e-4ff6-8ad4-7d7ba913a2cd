namespace OSP.Notification.Application.Features.Template;

public class Create<PERSON>ontent<PERSON>andler(IGenericRepository<TemplateContent, long> repository)
    : IRequestHandler<CreateContentCommand, long>
{
    public async Task<long> Handle(CreateContentCommand request, CancellationToken cancellationToken)
    {
        var content = new TemplateContent
        {
            TemplateId = request.TemplateId,
            Channel = request.Channel,
            Language = request.Language,
            Title = request.Title,
            Content = request.Content
        };

        await repository.InsertAsync(content);
        return content.Id;
    }
}