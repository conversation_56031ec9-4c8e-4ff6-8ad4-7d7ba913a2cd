﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>OSP.Notification.Api</RootNamespace>
    </PropertyGroup>

    <PropertyGroup>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\osp-common-be-dotnet\src\OSP.Common.Api\OSP.Common.Api.csproj"/>
        <ProjectReference Include="..\..\..\osp-common-be-dotnet\src\OSP.Common.Application\OSP.Common.Application.csproj"/>
        <ProjectReference Include="..\..\..\osp-common-be-dotnet\src\OSP.Common.Domain\OSP.Common.Domain.csproj"/>
        <ProjectReference Include="..\..\..\osp-common-be-dotnet\src\OSP.Common.Infrastructure\OSP.Common.Infrastructure.csproj"/>
        <ProjectReference Include="..\OSP.Notification.Application\OSP.Notification.Application.csproj"/>
        <ProjectReference Include="..\OSP.Notification.Domain\OSP.Notification.Domain.csproj"/>
        <ProjectReference Include="..\OSP.Notification.Infrastructure\OSP.Notification.Infrastructure.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Resources\ApiMessages.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>ApiMessages.resx</DependentUpon>
        </Compile>
        <Compile Update="Resources\ValidationMessages.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>ValidationMessages.resx</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="Resources\ApiMessages.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>ApiMessages.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Resources\ValidationMessages.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>ValidationMessages.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.9">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>
