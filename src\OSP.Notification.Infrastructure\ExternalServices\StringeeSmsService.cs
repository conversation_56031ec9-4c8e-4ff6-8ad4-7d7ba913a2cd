using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;
using OSP.Notification.Domain.Config;

namespace OSP.Notification.Infrastructure.ExternalServices;

public class StringeeSmsService(
    IHttpClientFactory httpClientFactory,
    IOptions<StringeeSmsOptions> options,
    ILogger<StringeeSmsService> logger)
    : ISmsService
{
    private readonly StringeeSmsOptions _options = options.Value;

    public async Task SendAsync(SmsMessage message, CancellationToken cancellationToken = default)
    {
        if (!message.To.Any())
        {
            throw new ArgumentException("message must have at least one to send");
        }

        var sms = message.To.Select(phone => new
        {
            From = _options.From,
            To = phone,
            Text = message.Content
        });

        var payload = new
        {
            Sms = sms
        };

        // Stringee dùng HTTP Basic Auth (API Key SID/Secret)
        var byteArray = Encoding.ASCII.GetBytes($"{_options.ApiKeySid}:{_options.ApiKeySecret}");
        var httpClient = httpClientFactory.CreateClient();
        httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));

        var response = await httpClient.PostAsJsonAsync("https://api.stringee.com/v1/sms", payload, cancellationToken);

        if (response.IsSuccessStatusCode)
        {
            logger.LogInformation("SMS sent to {To}. Content={Content}", message.To, message.Content);
        }
        else
        {
            var body = await response.Content.ReadAsStringAsync(cancellationToken);
            logger.LogError("Failed to send SMS via Stringee. StatusCode={Status}, Body={Body}",
                response.StatusCode, body);
            throw new InvalidOperationException(
                $"Failed to send SMS via Stringee. StatusCode={response.StatusCode}, Body={body}");
        }
    }

    public async Task SendBatchAsync(SmsMessage message, int batchSize = 100, int maxParallel = 5,
        CancellationToken cancellationToken = default)
    {
        var batches = message.To
            .Select((email, index) => new { email, index })
            .GroupBy(x => x.index / batchSize, x => x.email);

        using var semaphore = new SemaphoreSlim(maxParallel);

        var tasks = batches.Select(async batch =>
        {
            await semaphore.WaitAsync();

            var messageCopy = new SmsMessage
            {
                To = batch,
                Content = message.Content
            };

            try
            {
                await SendAsync(messageCopy, cancellationToken);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
    }
}