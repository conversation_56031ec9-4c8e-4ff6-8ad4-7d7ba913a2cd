using OSP.Common.Domain.Enums;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Models;
using OSP.Notification.Application.Strategies.NotificationStrategy;

namespace OSP.Notification.Infrastructure.Strategies.NotificationStrategy;

public class EmailNotificationStrategy(IEmailService emailService) : INotificationStrategy
{
    public NotificationChannel Channel => NotificationChannel.Email;

    public Task SendAsync(NotificationPayload payload)
    {
        var message = new EmailMessage
        {
            To = payload.Emails,
            Subject = payload.Title!,
            Body = payload.Content,
            IsHtml = true
        };
        return emailService.SendBatchAsync(message);
    }
}