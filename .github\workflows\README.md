# GitHub Workflows - Flexible Runner System

## Tổng quan

Hệ thống workflows đã được refactor để sử dụng **Flexible Runner** và **Reusable Workflows**, giảm thiểu code duplication và tăng tính bảo trì.

## Kiến trúc

### 🔄 Reusable Workflows

#### 1. `reusable-flexible-runner.yml`
Workflow tái sử dụng chính cho việc setup môi trường và chạy jobs với flexible runner.

**Tính năng:**
- ✅ Flexible runner selection (input > repository variable > fallback)
- ✅ .NET setup và configuration
- ✅ NuGet packages caching
- ✅ Automatic .NET dependencies installation
- ✅ Custom working directory
- ✅ Custom commands execution

**Inputs:**
```yaml
runner_choice: 'ubuntu-latest|macos-latest'   # Chọn runner
job_name: 'Tên job'                           # Tên hiển thị
setup_dotnet: true/false                      # Setup .NET
dotnet_version: '9.0'                         # Phiên bản .NET
working_directory: '.'                        # Th<PERSON> mục làm việc
run_command: 'dotnet build'                   # Command chạy
install_dependencies: 'none|backend'          # Cài dependencies
```

#### 2. `reusable-lark-notification.yml`
Workflow tái sử dụng cho việc gửi notification tới Lark.

**Tính năng:**
- ✅ Multiple notification types (PR, Issue, Requirements)
- ✅ Auto content generation
- ✅ Custom header templates
- ✅ Timezone formatting (GMT+7)
- ✅ Project code integration

### 🔧 .NET Backend Workflows

#### 1. `ci-build.yml` ➜ **.NET Build Pipeline**
- ✅ .NET project build và testing
- ✅ Path-based selective building
- ✅ NuGet packages caching
- ✅ dotnet restore, build, test

#### 2. `flexible-runner.yml` ➜ **.NET Development**
- ✅ Sử dụng `reusable-flexible-runner.yml`
- ✅ .NET build job type selection
- ✅ Dynamic .NET environment setup

#### 3. `copilot-setup-steps.yml` ➜ **.NET Environment**
- ✅ .NET setup cho Copilot
- ✅ NuGet dependencies installation
- ✅ dotnet restore validation

## 🎯 Lợi ích cho .NET Backend

### 1. **Tối ưu hóa cho .NET**
- **NuGet Caching:** Faster dependency resolution
- **dotnet CLI:** Native .NET build commands
- **.NET 9 Support:** Latest .NET features và performance

### 2. **Backend-focused Architecture**
- **No Frontend Overhead:** Loại bỏ Node.js, React, TypeScript setup
- **Clean Structure:** Chỉ focus vào backend concerns
- **Faster Builds:** Không có unnecessary frontend dependencies

### 3. **Flexible Runner Support**
- **Cross-platform:** Ubuntu, macOS, Windows runners
- **Self-hosted Ready:** Easy integration với on-premise runners
- **Cost Optimization:** Smart runner selection

## 📋 .NET Usage Examples

### Sử dụng cho .NET Build

```yaml
jobs:
  build-dotnet:
    uses: ./.github/workflows/reusable-flexible-runner.yml
    with:
      job_name: '.NET 9 Backend Build'
      setup_dotnet: true
      dotnet_version: '9.0'
      install_dependencies: 'backend'
      run_command: |
        echo "🔨 Building .NET 9 Backend..."
        dotnet restore
        dotnet build --configuration Release --no-restore
        dotnet test --configuration Release --no-build --verbosity normal
```

## ⚙️ .NET Configuration

### Repository Variables
- `DEFAULT_RUNNER_LABEL`: Runner mặc định (vd: `ubuntu-latest`)
- `PROJECT_CODE`: Mã dự án cho notifications

### Repository Secrets
- `LARK_CHAT_GROUP_NOTIFICATION`: Lark webhook URL

### .NET Configuration Files
- `global.json`: Lock .NET SDK version
- `Directory.Build.props`: Shared MSBuild properties
- `nuget.config`: NuGet sources configuration

## 🔍 .NET Migration Status

| Workflow                               | Status         | Notes                                     |
| -------------------------------------- | -------------- | ----------------------------------------- |
| `ci-build.yml`                         | ✅ **Complete** | .NET build pipeline                    |
| `reusable-flexible-runner.yml`         | ✅ **Complete** | .NET setup và caching                    |
| `flexible-runner.yml`                  | ✅ **Complete** | .NET job types                           |
| `copilot-setup-steps.yml`              | ✅ **Complete** | .NET Copilot environment                 |
| `pr-notification.yml`                  | ✅ **Complete** | Backend-focused notifications            |
| `requirements-change-review.yml`       | 🟡 **Minimal**  | Generic workflow, works with .NET        |

## 🚀 .NET Best Practices

### 1. **Dependencies Management**
- Sử dụng `dotnet restore` trước build
- Cache NuGet packages để tăng tốc độ
- Kiểm tra vulnerability với `dotnet list package --vulnerable`

### 2. **Build Configuration**
- Build với `--configuration Release` cho production
- Sử dụng `--no-restore` để tránh duplicate restore
- Run tests với `--no-build` sau khi build

### 3. **Project Structure**
- Follow Clean Architecture principles
- Separate concerns theo layers
- Sử dụng `global.json` để lock SDK version
