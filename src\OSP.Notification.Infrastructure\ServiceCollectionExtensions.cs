﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using OSP.Common.Infrastructure;
using OSP.Notification.Application.ExternalServices;
using OSP.Notification.Application.Strategies.NotificationStrategy;
using OSP.Notification.Domain.Config;
using OSP.Notification.Infrastructure.Data;
using OSP.Notification.Infrastructure.ExternalServices;
using OSP.Notification.Infrastructure.Strategies.NotificationStrategy;

namespace OSP.Notification.Infrastructure;

/// <summary>
///     Phương thức mở rộng để đăng ký các dịch vụ của Core.Infrastructure vào container DI.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    ///     Đăng ký các dịch vụ cốt lõi của Infrastructure vào container DI.
    /// </summary>
    /// <param name="services">Collection dịch vụ.</param>
    /// <param name="configuration">Cấu hình để lấy chuỗi kết nối.</param>
    /// <returns>Collection dịch vụ đã được đăng ký.</returns>
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.RegisterDbContext<NotificationContext>(configuration);

        services.AddEmailServices(configuration)
            .AddSmsService(configuration)
            .AddZaloService(configuration);

        services.AddNotificationStrategy();

        services.AddHttpClient<IUserService, UserService>(c =>
            {
                c.BaseAddress = new Uri("http://user-service"); // Thay đổi URL base tùy theo cấu hình thực tế
            })
            .ConfigDefaultsHttpClient();


        return services;
    }

    public static IServiceCollection AddSmsService(this IServiceCollection services, IConfiguration configuration)
    {
        // Bind options
        services.Configure<SmsProviderOptions>(configuration.GetSection("Sms"));
        services.Configure<StringeeSmsOptions>(configuration.GetSection("Sms:Stringee"));

        services.AddScoped<StringeeSmsService>();

        services.AddScoped<ISmsService, SmsServiceFactory>();

        return services;
    }

    public static IServiceCollection AddZaloService(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ZaloOptions>(configuration.GetSection("Zalo"));

        services.AddScoped<IZaloService, ZaloService>();

        return services;
    }

    public static IServiceCollection AddEmailServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Bind options
        services.Configure<EmailProviderOptions>(configuration.GetSection("Email"));
        services.Configure<SmtpOptions>(configuration.GetSection("Email:Smtp"));
        services.Configure<SendGridOptions>(configuration.GetSection("Email:SendGrid"));

        // Register both implementations
        services.AddSingleton<IRoundRobinEmailService, RoundRobinEmailService>();
        services.AddScoped<SmtpEmailService>();
        services.AddScoped<SendGridEmailService>();

        // Register factory as the main IEmailService
        services.AddScoped<IEmailService, EmailServiceFactory>();

        return services;
    }

    public static IServiceCollection AddNotificationStrategy(this IServiceCollection services)
    {
        services.AddScoped<INotificationStrategy, EmailNotificationStrategy>();
        services.AddScoped<INotificationStrategy, SmsNotificationStrategy>();
        services.AddScoped<INotificationStrategy, InternalNotificationStrategy>();
        services.AddScoped<INotificationStrategy, ZaloNotificationStrategy>();
        // ...Đăng ký các strategy khác cho Zalo, Telegram, etc.

        return services;
    }
}